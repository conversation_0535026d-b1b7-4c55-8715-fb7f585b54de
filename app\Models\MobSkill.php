<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MobSkill extends Model
{
    protected $table = 'mob_skills';

    protected $fillable = [
        'mob_id',
        'skill_id',
        'skill_template_id',
        'chance',
        'custom_cooldown',
        'custom_duration',
        'is_disabled',
        'cooldown_remaining',
        'cooldown_ends_at',
        'last_used_at'
    ];

    protected $casts = [
        'cooldown_ends_at' => 'datetime',
        'last_used_at' => 'datetime',
        'chance' => 'integer',
        'custom_cooldown' => 'integer',
        'custom_duration' => 'integer',
        'is_disabled' => 'boolean',
        'cooldown_remaining' => 'integer'
    ];

    /**
     * Связь с мобом
     */
    public function mob(): BelongsTo
    {
        return $this->belongsTo(Mob::class, 'mob_id');
    }

    /**
     * Связь с умением
     */
    public function skill(): BelongsTo
    {
        return $this->belongsTo(Skill::class, 'skill_id');
    }

    /**
     * Связь с шаблоном скилла
     */
    public function skillTemplate(): BelongsTo
    {
        return $this->belongsTo(MobSkillTemplate::class, 'skill_template_id');
    }

    /**
     * Проверить, доступен ли скилл для использования (не на кулдауне)
     */
    public function isAvailable(): bool
    {
        if (!$this->cooldown_ends_at) {
            return true;
        }

        return now()->gte($this->cooldown_ends_at);
    }

    /**
     * Установить кулдаун для скилла
     */
    public function setCooldown(int $seconds): void
    {
        $this->update([
            'cooldown_ends_at' => now()->addSeconds($seconds),
            'last_used_at' => now()
        ]);
    }

    /**
     * Получить эффективную длительность скилла (индивидуальную или из шаблона)
     */
    public function getEffectiveDuration(): int
    {
        // Если задана индивидуальная длительность, используем её
        if ($this->custom_duration !== null) {
            return $this->custom_duration;
        }

        // Иначе используем длительность из шаблона
        if ($this->skillTemplate) {
            return $this->skillTemplate->duration ?? 0;
        }

        return 0;
    }



    /**
     * Получить оставшееся время кулдауна в секундах
     */
    public function getRemainingCooldown(): int
    {
        if (!$this->cooldown_ends_at || now()->gte($this->cooldown_ends_at)) {
            return 0;
        }

        return now()->diffInSeconds($this->cooldown_ends_at);
    }

    /**
     * Скоуп для доступных скиллов (не на кулдауне)
     */
    public function scopeAvailable($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('cooldown_ends_at')
                ->orWhere('cooldown_ends_at', '<=', now());
        });
    }

    /**
     * Скоуп для скиллов на кулдауне
     */
    public function scopeOnCooldown($query)
    {
        return $query->where('cooldown_ends_at', '>', now());
    }

    /**
     * Скоуп для активных (не отключенных) скиллов
     */
    public function scopeActive($query)
    {
        return $query->where('is_disabled', false);
    }

    /**
     * Получить эффективный шанс срабатывания скилла
     * Использует индивидуальный шанс, если задан, иначе шанс из шаблона
     */
    public function getEffectiveChance(): int
    {
        if ($this->chance !== null) {
            return $this->chance;
        }

        return $this->skillTemplate?->chance ?? 10;
    }

    /**
     * Получить эффективный кулдаун скилла
     * Использует индивидуальный кулдаун, если задан, иначе кулдаун из шаблона
     */
    public function getEffectiveCooldown(): int
    {
        if ($this->custom_cooldown !== null) {
            return $this->custom_cooldown;
        }

        return $this->skillTemplate?->cooldown ?? 0;
    }

    /**
     * Проверить, активен ли скилл (не отключен и есть шаблон)
     */
    public function isActive(): bool
    {
        return !$this->is_disabled && $this->skillTemplate?->is_active === true;
    }

    /**
     * Проверить, использует ли скилл индивидуальные настройки
     */
    public function hasCustomSettings(): bool
    {
        return $this->chance !== null ||
            $this->custom_cooldown !== null ||
            $this->custom_duration !== null ||
            $this->is_disabled === true;
    }

    /**
     * Сбросить все индивидуальные настройки к значениям из шаблона
     */
    public function resetToTemplate(): void
    {
        $this->update([
            'chance' => null,
            'custom_cooldown' => null,
            'custom_duration' => null,
            'is_disabled' => false
        ]);
    }
}
