<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Скиллы моба: {{ $mob->name }} - Админ панель</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="bg-[#1a1814] text-[#d4cbb0] min-h-screen">
    <div class="container mx-auto px-4 py-8">
        {{-- Заголовок страницы --}}
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-[#e4d7b0] mb-2">
                        ⚙️ Настройка скиллов: {{ $mob->name }}
                    </h1>
                    <p class="text-[#998d66]">Индивидуальные настройки скиллов для данного моба</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('admin.mob-skills.mob-skills-index') }}" 
                       class="bg-gradient-to-b from-[#2a2722] to-[#1a1814] hover:from-[#3b3629] hover:to-[#2a2722] text-[#d4cbb0] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        ← Назад к списку
                    </a>
                    <a href="{{ route('admin.mob-skills.assign', ['mob_id' => $mob->id]) }}" 
                       class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-4 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                        🔗 Привязать скилл
                    </a>
                </div>
            </div>
        </div>

        {{-- Информация о мобе --}}
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] p-6 mb-8">
            <div class="flex items-center">
                @if($mob->icon)
                    <img src="{{ asset($mob->icon) }}" alt="{{ $mob->name }}" class="w-16 h-16 rounded mr-4">
                @else
                    <div class="w-16 h-16 bg-[#3b3629] rounded flex items-center justify-center mr-4 text-2xl">👹</div>
                @endif
                <div class="flex-1">
                    <h2 class="text-2xl font-bold text-[#e4d7b0] mb-1">{{ $mob->name }}</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                            <span class="text-[#998d66]">Локация:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $mob->getEffectiveLocation() ?? 'Не указана' }}</span>
                        </div>
                        <div>
                            <span class="text-[#998d66]">HP:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $mob->max_hp ?? $mob->hp }}</span>
                        </div>
                        <div>
                            <span class="text-[#998d66]">Опыт:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $mob->experience_reward ?? 0 }}</span>
                        </div>
                        <div>
                            <span class="text-[#998d66]">Скиллов:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $mobSkills->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Список скиллов --}}
        @if($mobSkills->count() > 0)
        <div class="space-y-6">
            @foreach($mobSkills as $mobSkill)
            @php
                $template = $mobSkill->skillTemplate;
                $hasCustomSettings = $mobSkill->hasCustomSettings();
            @endphp
            <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] overflow-hidden {{ $mobSkill->is_disabled ? 'opacity-60' : '' }}">
                <div class="px-6 py-4 border-b border-[#3b3629] flex items-center justify-between">
                    <div class="flex items-center">
                        @if($template->icon)
                            <img src="{{ asset($template->icon) }}" alt="{{ $template->name }}" class="w-10 h-10 rounded mr-3">
                        @else
                            <div class="w-10 h-10 bg-[#3b3629] rounded flex items-center justify-center mr-3">⚔️</div>
                        @endif
                        <div>
                            <h3 class="text-lg font-semibold text-[#e4d7b0] flex items-center">
                                {{ $template->name }}
                                @if($hasCustomSettings)
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-[#c1a96e] text-[#1a1814]">
                                        Настроен
                                    </span>
                                @endif
                                @if($mobSkill->is_disabled)
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-[#6e3f35] text-[#f8eac2]">
                                        Отключен
                                    </span>
                                @endif
                            </h3>
                            <p class="text-sm text-[#998d66]">{{ $template->description }}</p>
                        </div>
                    </div>
                    
                    <div class="flex space-x-2">
                        @if($hasCustomSettings)
                            <form method="POST" action="{{ route('admin.mob-skills.reset-mob-skill', $mobSkill) }}" class="inline">
                                @csrf
                                @method('PATCH')
                                <button type="submit" 
                                        class="bg-gradient-to-b from-[#6e3f35] to-[#3c221b] hover:from-[#7a4a3e] hover:to-[#4a2a20] text-[#f8eac2] px-3 py-1 rounded border border-[#3b3629] transition duration-300 text-xs"
                                        onclick="return confirm('Сбросить все индивидуальные настройки к значениям из шаблона?')">
                                    🔄 Сбросить
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
                
                <div class="p-6">
                    <form method="POST" action="{{ route('admin.mob-skills.update-mob-skill', $mobSkill) }}">
                        @csrf
                        @method('PATCH')
                        
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                            {{-- Шанс срабатывания --}}
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">
                                    Шанс срабатывания (%)
                                </label>
                                <div class="space-y-2">
                                    <input type="number" name="chance" min="1" max="100" 
                                           value="{{ $mobSkill->chance }}"
                                           placeholder="{{ $template->chance }} (из шаблона)"
                                           class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-3 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                                    <div class="text-xs text-[#998d66]">
                                        Шаблон: {{ $template->chance }}% | 
                                        Эффективно: {{ $mobSkill->getEffectiveChance() }}%
                                    </div>
                                </div>
                            </div>
                            
                            {{-- Кулдаун --}}
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">
                                    Кулдаун (секунды)
                                </label>
                                <div class="space-y-2">
                                    <input type="number" name="custom_cooldown" min="0"
                                           value="{{ $mobSkill->custom_cooldown }}"
                                           placeholder="{{ $template->cooldown }} (из шаблона)"
                                           class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-3 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                                    <div class="text-xs text-[#998d66]">
                                        Шаблон: {{ $template->cooldown }}с |
                                        Эффективно: {{ $mobSkill->getEffectiveCooldown() }}с
                                    </div>
                                </div>
                            </div>

                            {{-- Длительность эффекта --}}
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">
                                    Длительность эффекта (секунды)
                                </label>
                                <div class="space-y-2">
                                    <input type="number" name="custom_duration" min="0"
                                           value="{{ $mobSkill->custom_duration }}"
                                           placeholder="{{ $template->duration }} (из шаблона)"
                                           class="w-full bg-[#1a1814] border border-[#3b3629] rounded-lg px-3 py-2 text-[#d4cbb0] focus:border-[#c1a96e] focus:outline-none transition duration-300">
                                    <div class="text-xs text-[#998d66]">
                                        Шаблон: {{ $template->duration }}с |
                                        Эффективно: {{ $mobSkill->getEffectiveDuration() }}с
                                    </div>
                                </div>
                            </div>
                            
                            {{-- Статус --}}
                            <div>
                                <label class="block text-sm font-medium text-[#c1a96e] mb-2">
                                    Статус скилла
                                </label>
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input type="hidden" name="is_disabled" value="0">
                                        <input type="checkbox" name="is_disabled" value="1" 
                                               {{ $mobSkill->is_disabled ? 'checked' : '' }}
                                               class="rounded border-[#3b3629] text-[#c1a96e] focus:ring-[#c1a96e] focus:ring-offset-0 bg-[#1a1814]">
                                        <span class="ml-2 text-sm text-[#d4cbb0]">Отключить скилл</span>
                                    </label>
                                    <div class="text-xs text-[#998d66]">
                                        Отключенные скиллы не будут использоваться мобом
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end">
                            <button type="submit" 
                                    class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-2 rounded-lg border border-[#3b3629] transition duration-300">
                                💾 Сохранить настройки
                            </button>
                        </div>
                    </form>
                </div>
                
                {{-- Информация о шаблоне --}}
                <div class="px-6 py-3 bg-[#1a1814] border-t border-[#3b3629]">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-[#998d66]">
                        <div>
                            <span>Тип эффекта:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $template->effect_type }}</span>
                        </div>
                        <div>
                            <span>Цель:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $template->target_type }}</span>
                        </div>
                        <div>
                            <span>Длительность:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $template->duration }}с</span>
                        </div>
                        <div>
                            <span>Приоритет:</span>
                            <span class="text-[#d4cbb0] ml-1">{{ $template->priority }}</span>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        @else
        <div class="bg-gradient-to-br from-[#2a2722] to-[#1a1814] rounded-lg border border-[#3b3629] text-center py-12">
            <div class="text-6xl mb-4">⚔️</div>
            <h3 class="text-xl font-semibold text-[#e4d7b0] mb-2">У моба нет скиллов</h3>
            <p class="text-[#998d66] mb-6">Привяжите скиллы к этому мобу для настройки</p>
            <a href="{{ route('admin.mob-skills.assign', ['mob_id' => $mob->id]) }}" 
               class="bg-gradient-to-b from-[#2f473c] to-[#1e2e27] hover:from-[#3a5a47] hover:to-[#243529] text-[#f8eac2] px-6 py-3 rounded-lg border border-[#3b3629] transition duration-300">
                🔗 Привязать скилл
            </a>
        </div>
        @endif
    </div>

    {{-- Уведомления --}}
    @if(session('success'))
    <div class="fixed top-4 right-4 bg-gradient-to-r from-[#2f473c] to-[#1e2e27] border border-[#3b3629] text-[#f8eac2] px-6 py-3 rounded-lg shadow-lg z-50">
        <div class="flex items-center">
            <span class="text-xl mr-2">✅</span>
            {{ session('success') }}
        </div>
    </div>
    @endif

    @if(session('error'))
    <div class="fixed top-4 right-4 bg-gradient-to-r from-[#6e3f35] to-[#3c221b] border border-[#3b3629] text-[#f8eac2] px-6 py-3 rounded-lg shadow-lg z-50">
        <div class="flex items-center">
            <span class="text-xl mr-2">❌</span>
            {{ session('error') }}
        </div>
    </div>
    @endif
</body>
</html>
