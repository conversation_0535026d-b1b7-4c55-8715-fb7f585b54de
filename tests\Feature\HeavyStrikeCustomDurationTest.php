<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Models\ActiveEffect;
use App\Models\MineLocation;
use App\Services\SkillService;

class HeavyStrikeCustomDurationTest extends TestCase
{
    protected $testUser;
    protected $testMob;
    protected $testMineLocation;
    protected $heavyStrikeTemplate;
    protected $skillService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Создаем тестового пользователя
        $this->testUser = User::where('name', 'admin')->first();
        if (!$this->testUser) {
            $this->testUser = User::factory()->create(['name' => 'admin']);
        }

        // Создаем тестовую локацию рудника
        $this->testMineLocation = MineLocation::where('name', 'тест мобов руд')->first();
        if (!$this->testMineLocation) {
            $this->testMineLocation = MineLocation::create([
                'name' => 'тест мобов руд',
                'location_id' => 1,
                'description' => 'Тестовая локация для мобов',
                'is_active' => true
            ]);
        }

        // Создаем тестового моба
        $this->testMob = Mob::where('name', 'имимим')->first();
        if (!$this->testMob) {
            $this->testMob = Mob::create([
                'name' => 'имимим',
                'hp' => 100,
                'max_hp' => 100,
                'location' => 'тест мобов руд',
                'mine_location_id' => $this->testMineLocation->id
            ]);
        }

        // Получаем шаблон скилла "Тяжелый удар"
        $this->heavyStrikeTemplate = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
        if (!$this->heavyStrikeTemplate) {
            $this->heavyStrikeTemplate = MobSkillTemplate::create([
                'name' => 'Тяжелый удар',
                'description' => 'Мощный удар, который может оглушить противника',
                'icon' => 'assets/skills/mobs/skillHeavyStrike.png',
                'effect_type' => 'stun',
                'effect_data' => [
                    'duration' => 5,
                    'disable_skills' => true,
                    'disable_movement' => true,
                    'disable_actions' => true,
                    'message' => '⚡ Вы оглушены тяжелым ударом и не можете действовать!'
                ],
                'chance' => 30,
                'cooldown' => 20,
                'duration' => 25, // Базовая длительность 25 секунд
                'target_type' => 'player',
                'min_health_percent' => 0,
                'max_health_percent' => 100,
                'is_active' => true,
                'priority' => 5
            ]);
        }

        $this->skillService = app(SkillService::class);
    }

    protected function tearDown(): void
    {
        // Очищаем эффекты стана после каждого теста
        ActiveEffect::where('effect_data->type', 'stun')->delete();
        
        parent::tearDown();
    }

    /** @test */
    public function test_heavy_strike_uses_template_duration_when_no_custom_duration()
    {
        // Создаем скилл моба без кастомной длительности
        $mobSkill = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $this->heavyStrikeTemplate->id,
            'chance' => 100,
            'custom_duration' => null // Без кастомной длительности
        ]);

        // Проверяем, что используется длительность из шаблона
        $this->assertEquals(25, $mobSkill->getEffectiveDuration());

        // Применяем скилл
        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        $this->assertNotEmpty($results);
        $this->assertTrue($results[0]['success']);
        $this->assertEquals('stun', $results[0]['effect_type']);

        // Проверяем созданный эффект
        $stunEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $this->testUser->id)
            ->where('effect_data->type', 'stun')
            ->first();

        $this->assertNotNull($stunEffect);
        
        // Проверяем, что длительность соответствует шаблону (с погрешностью в 2 секунды)
        $actualDuration = now()->diffInSeconds($stunEffect->ends_at);
        $this->assertLessThanOrEqual(2, abs($actualDuration - 25));
    }

    /** @test */
    public function test_heavy_strike_uses_custom_duration_when_set()
    {
        // Создаем скилл моба с кастомной длительностью
        $customDuration = 10;
        $mobSkill = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $this->heavyStrikeTemplate->id,
            'chance' => 100,
            'custom_duration' => $customDuration
        ]);

        // Проверяем, что используется кастомная длительность
        $this->assertEquals($customDuration, $mobSkill->getEffectiveDuration());

        // Применяем скилл
        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        $this->assertNotEmpty($results);
        $this->assertTrue($results[0]['success']);
        $this->assertEquals('stun', $results[0]['effect_type']);

        // Проверяем созданный эффект
        $stunEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $this->testUser->id)
            ->where('effect_data->type', 'stun')
            ->first();

        $this->assertNotNull($stunEffect);
        
        // Проверяем, что длительность соответствует кастомной настройке
        $actualDuration = now()->diffInSeconds($stunEffect->ends_at);
        $this->assertLessThanOrEqual(2, abs($actualDuration - $customDuration));

        // Проверяем, что в данных эффекта сохранен ID скилла моба
        $this->assertEquals($mobSkill->id, $stunEffect->effect_data['mob_skill_id']);
    }

    /** @test */
    public function test_stun_effect_contains_correct_data()
    {
        // Создаем скилл моба
        $mobSkill = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $this->heavyStrikeTemplate->id,
            'chance' => 100,
            'custom_duration' => 15
        ]);

        // Применяем скилл
        $results = $this->skillService->processMobSkillTemplates($this->testMob, $this->testUser);

        $this->assertNotEmpty($results);
        $this->assertTrue($results[0]['success']);

        // Проверяем созданный эффект
        $stunEffect = ActiveEffect::where('target_type', 'player')
            ->where('target_id', $this->testUser->id)
            ->where('effect_data->type', 'stun')
            ->first();

        $this->assertNotNull($stunEffect);

        $effectData = $stunEffect->effect_data;
        
        // Проверяем все необходимые поля
        $this->assertEquals('stun', $effectData['type']);
        $this->assertTrue($effectData['disable_skills']);
        $this->assertTrue($effectData['disable_movement']);
        $this->assertTrue($effectData['disable_actions']);
        $this->assertStringContains('оглушены тяжелым ударом', $effectData['message']);
        $this->assertEquals($mobSkill->id, $effectData['mob_skill_id']);
        $this->assertEquals($this->heavyStrikeTemplate->id, $effectData['template_id']);
    }

    /** @test */
    public function test_multiple_custom_durations_work_independently()
    {
        // Создаем второго тестового моба
        $testMob2 = Mob::create([
            'name' => 'тестовый моб 2',
            'hp' => 100,
            'max_hp' => 100,
            'location' => 'тест мобов руд',
            'mine_location_id' => $this->testMineLocation->id
        ]);

        // Создаем скиллы с разными кастомными длительностями
        $mobSkill1 = MobSkill::create([
            'mob_id' => $this->testMob->id,
            'skill_template_id' => $this->heavyStrikeTemplate->id,
            'chance' => 100,
            'custom_duration' => 5
        ]);

        $mobSkill2 = MobSkill::create([
            'mob_id' => $testMob2->id,
            'skill_template_id' => $this->heavyStrikeTemplate->id,
            'chance' => 100,
            'custom_duration' => 20
        ]);

        // Проверяем, что каждый моб использует свою кастомную длительность
        $this->assertEquals(5, $mobSkill1->getEffectiveDuration());
        $this->assertEquals(20, $mobSkill2->getEffectiveDuration());

        // Очищаем тестового моба
        $testMob2->delete();
    }
}
