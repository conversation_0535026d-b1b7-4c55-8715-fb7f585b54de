<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\Location;
use App\Models\Mob;
use App\Models\MineLocation;
use App\Services\MineDetectionService;

// Загружаем Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔧 ИСПРАВЛЕНИЕ СИСТЕМЫ АТАК МОБОВ В РУДНИКАХ\n";
echo "=============================================\n\n";

// 1. Находим пользователя admin
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";

// 2. Очищаем старые истекшие метки
echo "\n🧹 ОЧИСТКА СТАРЫХ МЕТОК:\n";
$expiredMarks = MineMark::where('expires_at', '<', now())->get();
echo "Найдено истекших меток: {$expiredMarks->count()}\n";

foreach ($expiredMarks as $mark) {
    echo "- Удаляем метку ID: {$mark->id} (истекла: {$mark->expires_at})\n";
    $mark->delete();
}

// 3. Находим локацию и рудник
$location = Location::where('name', 'аааааааааааа')->first();
if (!$location) {
    echo "❌ Локация 'аааааааааааа' не найдена\n";
    exit(1);
}

$mineLocation = MineLocation::where('location_id', $location->id)->where('is_active', true)->first();
if (!$mineLocation) {
    echo "❌ Активный рудник в локации не найден\n";
    exit(1);
}

echo "✅ Локация: {$location->name} (ID: {$location->id})\n";
echo "✅ Рудник: {$mineLocation->name} (ID: {$mineLocation->id})\n";

// 4. Проверяем мобов
$mobs = Mob::where('location_id', $location->id)->where('hp', '>', 0)->get();
echo "✅ Живых мобов в локации: {$mobs->count()}\n";
foreach ($mobs as $mob) {
    echo "- {$mob->name} (ID: {$mob->id}), HP: {$mob->hp}/{$mob->max_hp}\n";
}

// 5. Устанавливаем правильную локацию игрока
echo "\n📍 УСТАНОВКА ЛОКАЦИИ ИГРОКА:\n";
if (!$user->statistics) {
    echo "❌ У игрока нет статистики\n";
    exit(1);
}

$user->statistics->current_location = $location->name;
$user->statistics->save();
echo "✅ Локация игрока установлена: {$location->name}\n";

// 6. Создаем новую активную метку
echo "\n🎯 СОЗДАНИЕ НОВОЙ МЕТКИ:\n";
$newMark = MineMark::create([
    'player_id' => $user->id,
    'mine_location_id' => $mineLocation->id,
    'location_id' => $location->id,
    'location_name' => $mineLocation->name,
    'expires_at' => now()->addMinutes(15), // 15 минут
    'is_active' => true,
    'attack_count' => 0,
    'last_attack_at' => null
]);

echo "✅ Создана новая метка ID: {$newMark->id}\n";
echo "⏰ Истекает: {$newMark->expires_at}\n";
echo "📍 Рудник: {$newMark->location_name}\n";

// 7. Проверяем сервис обнаружения
echo "\n🔧 ПРОВЕРКА СЕРВИСА ОБНАРУЖЕНИЯ:\n";
$mineDetectionService = app(MineDetectionService::class);

$markedPlayers = $mineDetectionService->getMarkedPlayersInMine($location->id, $mineLocation->id);
echo "Замеченных игроков в руднике: " . count($markedPlayers) . "\n";

foreach ($markedPlayers as $playerData) {
    echo "- Игрок: {$playerData['player_name']} (ID: {$playerData['player_id']})\n";
    echo "  Рудник: {$playerData['location_name']}\n";
    echo "  Истекает через: {$playerData['remaining_seconds']} секунд\n";
    echo "  Атак: {$playerData['attack_count']}\n";
    echo "  Последняя атака: " . ($playerData['last_attack_at'] ? date('Y-m-d H:i:s', $playerData['last_attack_at']) : 'Никогда') . "\n\n";
}

// 8. Тестируем автоатаку вручную
echo "⚔️ ТЕСТИРОВАНИЕ АВТОАТАКИ:\n";
try {
    $job = new \App\Jobs\MineAutoAttackJob();
    $job->handle(
        app(\App\Services\MineDetectionService::class),
        app(\App\Services\MineTargetDistributionService::class),
        app(\App\Services\BattleLogService::class),
        app(\App\Services\PlayerHealthService::class),
        app(\App\Services\CombatFormulaService::class),
        app(\App\Services\LogFormattingService::class)
    );
    echo "✅ Задача автоатаки выполнена успешно\n";
} catch (Exception $e) {
    echo "❌ Ошибка при выполнении автоатаки: {$e->getMessage()}\n";
}

// 9. Проверяем результат
echo "\n📊 ПРОВЕРКА РЕЗУЛЬТАТА:\n";
$updatedMark = MineMark::find($newMark->id);
if ($updatedMark) {
    echo "✅ Метка найдена:\n";
    echo "- Атак: {$updatedMark->attack_count}\n";
    echo "- Последняя атака: " . ($updatedMark->last_attack_at ?? 'Никогда') . "\n";
    echo "- Активна: " . ($updatedMark->is_active ? 'Да' : 'Нет') . "\n";
} else {
    echo "❌ Метка не найдена\n";
}

// 10. Проверяем HP игрока
$user->refresh();
$currentHP = $user->profile->getActualResources()['hp'] ?? 0;
$maxHP = $user->profile->getActualResources()['max_hp'] ?? 0;
echo "\n❤️ HP ИГРОКА:\n";
echo "Текущее HP: {$currentHP}/{$maxHP}\n";

echo "\n✅ Исправление завершено!\n";
echo "\n📋 ИНСТРУКЦИИ:\n";
echo "1. Планировщик должен быть запущен: php artisan schedule:work\n";
echo "2. Автоатаки будут происходить каждые 15-30 секунд\n";
echo "3. Проверьте логи: storage/logs/laravel.log\n";
