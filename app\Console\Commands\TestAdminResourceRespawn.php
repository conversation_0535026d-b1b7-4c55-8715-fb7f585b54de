<?php

namespace App\Console\Commands;

use App\Models\SpawnedResource;
use App\Models\Resource;
use App\Models\Location;
use App\Http\Controllers\Admin\LocationResourceController;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TestAdminResourceRespawn extends Command
{
    protected $signature = 'test:admin-resource-respawn {--cleanup : Удалить тестовые ресурсы}';
    protected $description = 'Тестирование системы респавна ресурсов через админку';

    public function handle()
    {
        if ($this->option('cleanup')) {
            return $this->cleanup();
        }

        $this->info('🧪 Тестирование системы респавна ресурсов через админку...');
        $this->newLine();

        // Шаг 1: Создаем тестовый ресурс через админку
        $this->info('1️⃣ Создание тестового ресурса через админку...');
        $testResource = $this->createResourceViaAdmin();
        
        if (!$testResource) {
            $this->error('❌ Не удалось создать тестовый ресурс');
            return Command::FAILURE;
        }

        $this->line("   ✅ Создан ресурс ID: {$testResource->id}");
        $this->line("   📊 Время респавна: {$testResource->respawn_time} минут");
        $this->line("   🎯 Шанс респавна: {$testResource->spawn_chance}%");
        $this->line("   📊 Активен: " . ($testResource->is_active ? 'Да' : 'Нет'));
        $this->line("   ⏰ Время респавна: " . ($testResource->respawn_at ? $testResource->respawn_at->format('Y-m-d H:i:s') : 'null'));
        $this->newLine();

        // Шаг 2: Редактируем ресурс через админку (делаем неактивным)
        $this->info('2️⃣ Редактирование ресурса через админку (делаем неактивным)...');
        $updatedResource = $this->updateResourceViaAdmin($testResource->id, [
            'is_active' => false,
            'durability' => 0,
            'respawn_time' => 5, // 5 минут
            'spawn_chance' => 100
        ]);

        if ($updatedResource) {
            $testResource->refresh();
            $this->line("   ✅ Ресурс обновлен");
            $this->line("   📊 Активен: " . ($testResource->is_active ? 'Да' : 'Нет'));
            $this->line("   💪 Прочность: {$testResource->durability}");
            $this->line("   ⏰ Время респавна: " . ($testResource->respawn_at ? $testResource->respawn_at->format('Y-m-d H:i:s') : 'null'));
            $this->line("   🕐 Текущее время: " . now()->format('Y-m-d H:i:s'));
        } else {
            $this->error('   ❌ Не удалось обновить ресурс');
            return Command::FAILURE;
        }
        $this->newLine();

        // Шаг 3: Проверяем, что время респавна установлено правильно
        $this->info('3️⃣ Проверка времени респавна...');
        if ($testResource->respawn_at) {
            $expectedTime = now()->addMinutes(5);
            $actualTime = $testResource->respawn_at;
            $diffInMinutes = abs($expectedTime->diffInMinutes($actualTime));
            
            if ($diffInMinutes <= 1) { // Допускаем погрешность в 1 минуту
                $this->line("   ✅ Время респавна установлено правильно");
                $this->line("   📊 Ожидаемое время: ~{$expectedTime->format('Y-m-d H:i:s')}");
                $this->line("   📊 Фактическое время: {$actualTime->format('Y-m-d H:i:s')}");
            } else {
                $this->error("   ❌ Время респавна установлено неправильно");
                $this->line("   📊 Ожидаемое время: ~{$expectedTime->format('Y-m-d H:i:s')}");
                $this->line("   📊 Фактическое время: {$actualTime->format('Y-m-d H:i:s')}");
                $this->line("   📊 Разница: {$diffInMinutes} минут");
            }
        } else {
            $this->error("   ❌ Время респавна не установлено");
        }
        $this->newLine();

        // Шаг 4: Редактируем ресурс с точным временем респавна
        $this->info('4️⃣ Установка точного времени респавна...');
        $exactRespawnTime = now()->addMinutes(10);
        $updatedResource = $this->updateResourceViaAdmin($testResource->id, [
            'is_active' => false,
            'durability' => 0,
            'respawn_time' => 5,
            'spawn_chance' => 100,
            'respawn_at' => $exactRespawnTime->format('Y-m-d\TH:i')
        ]);

        if ($updatedResource) {
            $testResource->refresh();
            $this->line("   ✅ Ресурс обновлен с точным временем");
            $this->line("   ⏰ Установленное время: {$exactRespawnTime->format('Y-m-d H:i:s')}");
            $this->line("   ⏰ Фактическое время: " . ($testResource->respawn_at ? $testResource->respawn_at->format('Y-m-d H:i:s') : 'null'));
            
            if ($testResource->respawn_at && $testResource->respawn_at->equalTo($exactRespawnTime)) {
                $this->line("   ✅ Точное время респавна установлено правильно");
            } else {
                $this->error("   ❌ Точное время респавна установлено неправильно");
            }
        } else {
            $this->error('   ❌ Не удалось обновить ресурс с точным временем');
        }
        $this->newLine();

        // Шаг 5: Результаты
        $this->info('5️⃣ Результаты тестирования:');
        if ($testResource->respawn_at && !$testResource->is_active) {
            $this->line("   ✅ Тест ПРОЙДЕН: Время респавна устанавливается правильно");
        } else {
            $this->error("   ❌ Тест ПРОВАЛЕН: Проблемы с установкой времени респавна");
        }
        
        $this->newLine();
        $this->info('💡 Для очистки тестовых данных запустите: php artisan test:admin-resource-respawn --cleanup');

        return Command::SUCCESS;
    }

    private function createResourceViaAdmin(): ?SpawnedResource
    {
        // Получаем первый доступный ресурс и локацию
        $resource = Resource::first();
        $location = Location::first();
        
        if (!$resource || !$location) {
            return null;
        }

        // Симулируем создание через админку
        return SpawnedResource::create([
            'resource_id' => $resource->id,
            'location_id' => $location->id,
            'is_active' => true,
            'durability' => 100,
            'max_durability' => 100,
            'respawn_time' => 3, // 3 минуты для тестирования
            'spawn_chance' => 100,
            'respawn_at' => null
        ]);
    }

    private function updateResourceViaAdmin(int $resourceId, array $data): bool
    {
        try {
            $resource = SpawnedResource::findOrFail($resourceId);
            
            // Симулируем логику из контроллера
            $validated = $data;
            
            // Логика установки времени респауна (как в контроллере)
            if (isset($data['respawn_at']) && $data['respawn_at']) {
                // Если указано точное время респавна
                $validated['respawn_at'] = Carbon::parse($data['respawn_at']);
                Log::info("Установлено точное время респавна для ресурса #{$resource->id}: {$validated['respawn_at']}");
            } else if (!$validated['is_active']) {
                // Если ресурс неактивен, рассчитываем время респавна автоматически
                $validated['respawn_at'] = Carbon::now()->addMinutes($validated['respawn_time']);
                Log::info("Рассчитано автоматическое время респавна для ресурса #{$resource->id}: {$validated['respawn_at']} (через {$validated['respawn_time']} минут)");
            } else {
                // Если ресурс активен, сбрасываем время респауна
                $validated['respawn_at'] = null;
                Log::info("Сброшено время респауна для активного ресурса #{$resource->id}");
            }

            $resource->update($validated);
            return true;
        } catch (\Exception $e) {
            Log::error("Ошибка при обновлении ресурса: " . $e->getMessage());
            return false;
        }
    }

    private function cleanup(): int
    {
        $this->info('🧹 Очистка тестовых ресурсов...');
        
        // Удаляем тестовые ресурсы (с коротким временем респавна)
        $deleted = SpawnedResource::whereIn('respawn_time', [3, 5])->delete();
        
        $this->line("   ✅ Удалено тестовых ресурсов: {$deleted}");
        
        return Command::SUCCESS;
    }
}
