<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;

class CheckPlayerStun
{
    /**
     * Обрабатывает входящий запрос.
     * Если игрок оглушен - просто обновляет текущую страницу.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Проверяем авторизацию
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $user = Auth::user();

        // Проверяем, есть ли эффект стана
        $isStunned = $user->isStunned();

        if ($isStunned) {
            // Для AJAX-запросов
            if ($request->ajax() || $request->expectsJson()) {
                return response()->json(['stun_error' => true], 403);
            }

            // ЗАЩИТА ОТ ЦИКЛИЧЕСКИХ ПЕРЕАДРЕСАЦИЙ
            // Проверяем, не находимся ли мы уже в цикле переадресаций
            $redirectCount = $this->getRedirectCount($user->id);
            if ($redirectCount >= 3) {
                // Если слишком много переадресаций, отправляем на безопасную страницу
                $this->clearRedirectCount($user->id);
                return redirect()->route('home')->with('error', '⚡ Вы оглушены и не можете действовать!');
            }

            // Увеличиваем счетчик переадресаций
            $this->incrementRedirectCount($user->id);

            // Просто обновляем текущую страницу
            return redirect()->back();
        } else {
            // Если игрок не оглушен, очищаем счетчик переадресаций
            $this->clearRedirectCount($user->id);
        }

        return $next($request);
    }

    /**
     * Получает количество переадресаций для пользователя
     */
    private function getRedirectCount(int $userId): int
    {
        return (int) cache()->get("stun_redirect_count:{$userId}", 0);
    }

    /**
     * Увеличивает счетчик переадресаций
     */
    private function incrementRedirectCount(int $userId): void
    {
        $count = $this->getRedirectCount($userId) + 1;
        cache()->put("stun_redirect_count:{$userId}", $count, 300); // 5 минут
    }

    /**
     * Очищает счетчик переадресаций
     */
    private function clearRedirectCount(int $userId): void
    {
        cache()->forget("stun_redirect_count:{$userId}");
    }
}
