<?php

require_once 'vendor/autoload.php';

use App\Jobs\MineAutoAttackJob;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use App\Services\BattleLogService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Services\LogFormattingService;

// Загружаем Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 ТЕСТИРОВАНИЕ РУЧНОЙ АТАКИ МОБОВ\n";
echo "==================================\n\n";

try {
    echo "🚀 Запуск MineAutoAttackJob...\n";
    
    $job = new MineAutoAttackJob();
    
    $result = $job->handle(
        app(MineDetectionService::class),
        app(MineTargetDistributionService::class),
        app(BattleLogService::class),
        app(PlayerHealthService::class),
        app(CombatFormulaService::class),
        app(LogFormattingService::class)
    );
    
    echo "✅ Задача выполнена успешно\n";
    
} catch (Exception $e) {
    echo "❌ Ошибка при выполнении задачи:\n";
    echo "Сообщение: {$e->getMessage()}\n";
    echo "Файл: {$e->getFile()}:{$e->getLine()}\n";
    echo "Трассировка:\n{$e->getTraceAsString()}\n";
}

// Проверяем результат
echo "\n📊 ПРОВЕРКА РЕЗУЛЬТАТА:\n";
$mark = \App\Models\MineMark::where('player_id', 7)->where('is_active', true)->first();

if ($mark) {
    echo "✅ Метка найдена:\n";
    echo "- ID: {$mark->id}\n";
    echo "- Атак: {$mark->attack_count}\n";
    echo "- Последняя атака: " . ($mark->last_attack_at ?? 'Никогда') . "\n";
    echo "- Активна: " . ($mark->is_active ? 'Да' : 'Нет') . "\n";
    echo "- Истекает: {$mark->expires_at}\n";
} else {
    echo "❌ Активная метка не найдена\n";
}

echo "\n✅ Тестирование завершено\n";
