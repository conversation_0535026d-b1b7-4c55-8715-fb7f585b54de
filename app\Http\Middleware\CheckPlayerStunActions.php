<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPlayerStunActions
{
    /**
     * Обрабатывает входящий запрос.
     * Блокирует ВСЕ игровые действия для оглушенных игроков.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Проверяем авторизацию
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Проверяем, есть ли эффект стана
        $isStunned = $user->isStunned();

        if ($isStunned) {
            $currentRoute = $request->route()->getName();

            // Определяем, является ли это игровым действием
            if ($this->isGameAction($currentRoute, $request)) {
                // Получаем сообщение об оглушении
                $stunMessage = $this->getStunMessage($user);

                // Для AJAX-запросов
                if ($request->ajax() || $request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'error' => 'stun_blocked',
                        'message' => $stunMessage
                    ], 403);
                }

                // ЗАЩИТА ОТ ЦИКЛИЧЕСКИХ ПЕРЕАДРЕСАЦИЙ
                // Проверяем, не находимся ли мы уже в цикле переадресаций
                $redirectCount = $this->getRedirectCount($user->id);
                if ($redirectCount >= 3) {
                    // Если слишком много переадресаций, отправляем на безопасную страницу
                    $this->clearRedirectCount($user->id);
                    return redirect()->route('home')->with('error', $stunMessage);
                }

                // Увеличиваем счетчик переадресаций
                $this->incrementRedirectCount($user->id);

                // Для обычных запросов - редирект назад с сообщением
                return redirect()->back()->with('error', $stunMessage);
            }
        } else {
            // Если игрок не оглушен, очищаем счетчик переадресаций
            $this->clearRedirectCount($user->id);
        }

        return $next($request);
    }

    /**
     * Определяет, является ли запрос игровым действием
     */
    private function isGameAction(string $routeName, Request $request): bool
    {
        // Список паттернов маршрутов, которые считаются игровыми действиями
        $gameActionPatterns = [
            // Боевые действия
            '*.attack*',
            '*.use_skill*',
            '*.use-skill*',
            '*.retaliate*',
            '*.change*target*',
            '*.select*',

            // Действия с ресурсами
            '*.hit*resource*',
            '*.select*resource*',

            // Перемещения между локациями
            'battle.outposts.*',
            'battle.mines.*',
            'dungeons.*',

            // Торговля и взаимодействия
            '*.trade*',
            '*.buy*',
            '*.sell*',
            '*.craft*',
            '*.melt*',
            '*.forge*',

            // Групповые действия
            'party.*',

            // Профессии и фермерство
            'professions.*',
            'farming.*',
            'masters.*',
        ];

        // Исключения - действия, которые разрешены даже при стане
        $allowedActions = [
            // Просмотр информации
            'inventory.index',
            'character.index',
            'character.stats',
            'character.skills',

            // Сообщения
            'messages.*',

            // Настройки
            'settings.*',

            // Главная страница
            'home',
            'dashboard',

            // Выход из игры
            'auth.logout',

            // API для проверки состояния
            'api.*stun*',

            // Страница поражения и возрождение
            'battle.defeat',
            'battle.respawn',
        ];

        // Проверяем исключения
        foreach ($allowedActions as $pattern) {
            if ($this->matchesPattern($routeName, $pattern)) {
                return false;
            }
        }

        // Проверяем игровые действия
        foreach ($gameActionPatterns as $pattern) {
            if ($this->matchesPattern($routeName, $pattern)) {
                return true;
            }
        }

        // Дополнительная проверка по HTTP методу и пути
        if ($request->isMethod('POST') || $request->isMethod('PUT') || $request->isMethod('DELETE')) {
            $path = $request->path();

            // Блокируем POST запросы к игровым разделам
            $gamePathPatterns = [
                'battle/*',
                'dungeons/*',
                'party/*',
                'professions/*',
                'farming/*',
                'masters/*',
                'shop/*',
                'market/*',
            ];

            foreach ($gamePathPatterns as $pattern) {
                if ($this->matchesPathPattern($path, $pattern)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Проверяет соответствие строки паттерну с поддержкой wildcards
     */
    private function matchesPattern(string $string, string $pattern): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        return preg_match('/^' . $pattern . '$/i', $string);
    }

    /**
     * Проверяет соответствие пути паттерну
     */
    private function matchesPathPattern(string $path, string $pattern): bool
    {
        $pattern = str_replace('*', '.*', $pattern);
        return preg_match('/^' . $pattern . '$/i', $path);
    }

    /**
     * Получает сообщение об оглушении
     */
    private function getStunMessage($user): string
    {
        // Получаем активный эффект оглушения
        $stunEffect = $user->activeEffects()
            ->get()
            ->first(function ($effect) {
                return $effect->isActive() && $effect->isStunEffect();
            });

        if ($stunEffect && isset($stunEffect->effect_data['message'])) {
            return $stunEffect->effect_data['message'];
        }

        return '⚡ Вы оглушены и не можете действовать!';
    }

    /**
     * Получает количество переадресаций для пользователя
     */
    private function getRedirectCount(int $userId): int
    {
        return (int) cache()->get("stun_redirect_count:{$userId}", 0);
    }

    /**
     * Увеличивает счетчик переадресаций
     */
    private function incrementRedirectCount(int $userId): void
    {
        $count = $this->getRedirectCount($userId) + 1;
        cache()->put("stun_redirect_count:{$userId}", $count, 300); // 5 минут
    }

    /**
     * Очищает счетчик переадресаций
     */
    private function clearRedirectCount(int $userId): void
    {
        cache()->forget("stun_redirect_count:{$userId}");
    }
}
