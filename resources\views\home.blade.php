<!DOCTYPE html>
<html lang="en">
<?php
use Illuminate\Support\Facades\Auth;
use App\Models\GuildInvitation;
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{-- CSRF-токен для AJAX-запросов --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Echoes of Eternity - Главная')</title>

    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/layout/server-time.js', 'resources/js/home/<USER>', 'resources/css/components/guild-invitation.css', 'resources/css/components/donation-button.css', 'resources/js/global/csrf.js', 'resources/js/global/notifications.js'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">


    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 bg-gradient-to-b from-[#4a4a3d] to-[#3b3a33] border-2 border-[#a6925e] rounded-lg flex-grow"
        style="background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.0))">
        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" :hasUnreadTicketComments="$hasUnreadTicketComments ?? false"
                :unreadTicketCommentsCount="$unreadTicketCommentsCount ?? 0" />
        </x-layout.hp-mp-bar>

        {{-- Отображение валюты --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Блок изображения локации для главной страницы --}}
        <x-layout.home-location-image title="Город" imagePath="assets/UI/banner-border.png" imageAlt="Город" />

        {{-- Сообщения --}}
        <div class="text-center flex justify-center space-x-1 max-w-[300px] mx-auto">
            @if (session('welcome_message'))
                <div class="bg-[#3b3a33] text-white p-4 rounded mb-2 mt-2 w-full">
                    {{ session('welcome_message') }}
                </div>
            @endif
        </div>

        {{-- Уведомление о приглашении в гильдию --}}
        @if(Auth::check())
            @php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            @endphp
            <x-layout.guild-invitation :guildInvitation="$guildInvitation" />
        @endif

        {{-- Уведомления об объявлениях --}}
        <x-layout.announcement-notifications :announcementData="$announcementData ?? null"
            :unviewedAnnouncements="$unviewedAnnouncements ?? collect()" />



        {{-- Основная --}}
        @php
            $navigationItems = [
                [
                    'route' => 'battle.index',
                    'title' => 'Сражение',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M14.5 17.5L3 6V3h3l11.5 11.5M14.5 17.5l3.5 3.5l-3-3l-3.5-3.5M14.5 17.5l3-3" /><path d="M13 6l-3 3" /></svg>'
                ],
                [
                    'route' => 'shop.index',
                    'title' => 'Торговля',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="9" cy="21" r="1" /><circle cx="20" cy="21" r="1" /><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6" /></svg>'
                ],
                [
                    'route' => 'masters.index',
                    'title' => 'Таверна',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 21v-6a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v6" /><path d="M19 21V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v16" /><path d="M3 7h18" /><path d="M5 21h14" /></svg>'
                ],
                [
                    'route' => 'professions.index',
                    'title' => 'Профессии',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 4.5c-1.5 0-2.5 1-2.5 2.5 0 1.5 1 2.5 2.5 2.5s2.5-1 2.5-2.5c0-1.5-1-2.5-2.5-2.5z" /><path d="M19.5 16.5L12 21l-7.5-4.5v-6L12 6l7.5 4.5v6z" /><path d="M12 12v9" /><path d="M7.5 9.5L12 12l4.5-2.5" /></svg>'
                ],
                [
                    'route' => 'farming.index',
                    'title' => 'Фермерство',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" /><path d="M8 10h8" /><path d="M12 6v8" /></svg>'
                ],
                [
                    'route' => 'square.index',
                    'title' => 'Площадь',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>'
                ],
                [
                    'route' => 'guilds.index',
                    'title' => 'Гильдии',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M9 21v-6a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v6" /><path d="M19 21V5a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v16" /><path d="M3 7h18" /><path d="M5 21h14" /></svg>'
                ],
                [
                    'route' => 'test-top',
                    'title' => 'Топ тестировщиков',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1Z"/></svg>'
                ],
                [
                    'route' => 'top-players.index',
                    'title' => 'Топ игроков',
                    'icon' => '<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 text-[#e5b769] group-hover:text-[#f0d89e]" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 6l2 14H6l2-14" /><path d="M12 2v4" /><path d="M8 2h8" /><circle cx="12" cy="11" r="3" /></svg>'
                ]
            ];
        @endphp

        <x-layout.main-navigation :navigationItems="$navigationItems" />

        {{-- Кнопка пожертвований --}}
        <div class="mt-1">
            <x-layout.donation-button />
        </div>
    </div>

    {{-- Нижние кнопки навигации --}}
    @php
        $isStunned = Auth::user()->isStunned();
    @endphp
    <x-layout.navigation-buttons :isDisabled="$isStunned" />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />


</body>

</html>