<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\MineLocation;

// Загружаем Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔄 СОЗДАНИЕ СВЕЖЕЙ МЕТКИ ДЛЯ ТЕСТИРОВАНИЯ\n";
echo "=========================================\n\n";

// Удаляем старые метки
MineMark::where('player_id', 7)->delete();
echo "✅ Старые метки удалены\n";

// Создаем новую метку
$user = User::find(7);
$mineLocation = MineLocation::find(172);

$newMark = MineMark::create([
    'player_id' => $user->id,
    'mine_location_id' => $mineLocation->id,
    'location_id' => $mineLocation->location_id,
    'location_name' => $mineLocation->name,
    'expires_at' => now()->addMinutes(20),
    'is_active' => true,
    'attack_count' => 0,
    'last_attack_at' => null // Важно: НЕТ времени последней атаки
]);

echo "✅ Создана новая метка ID: {$newMark->id}\n";
echo "⏰ Истекает: {$newMark->expires_at}\n";
echo "🎯 Рудник: {$newMark->location_name}\n";
echo "⚔️ Атак: {$newMark->attack_count}\n";
echo "🕐 Последняя атака: " . ($newMark->last_attack_at ?? 'Никогда') . "\n";

echo "\n🚀 Метка готова для автоатак!\n";
