<?php

namespace App\Services;

use App\Models\MineMark;
use App\Models\User;
use App\Models\MineLocation;
use App\Models\ActiveEffect;
use App\Events\MineDetectionEvent;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * Сервис для управления системой обнаружения в рудниках
 * 
 * Когда игрок добывает ресурсы в рудниках, на него накладывается метка "Замечен",
 * которая делает его видимым для атак мобов в той же локации рудника.
 * Система аналогична ObeliskMarkService но для рудников.
 */
class MineDetectionService
{
    /**
     * Длительность метки обнаружения в секундах (5 минут)
     */
    const DETECTION_DURATION = 300;

    /**
     * Создать метку обнаружения для игрока в руднике
     *
     * @param User $player Игрок
     * @param MineLocation $mineLocation Локация рудника
     * @param int $duration Длительность метки в секундах
     * @return MineMark
     */
    public function createMark(
        User $player,
        MineLocation $mineLocation,
        int $duration = self::DETECTION_DURATION
    ): MineMark {
        DB::beginTransaction();

        try {
            // Деактивируем существующие метки игрока в этом руднике
            $this->deactivateExistingMarks($player->id, $mineLocation->id);

            // Создаем новую метку в БД
            $mark = MineMark::createMark(
                $player->id,
                $mineLocation->id,
                $mineLocation->location_id,
                $mineLocation->name,
                $duration
            );

            // Кэшируем в Redis
            $mark->cacheInRedisWithPreciseTTL();

            DB::commit();

            // Запускаем событие создания метки
            event(new MineDetectionEvent($player, $mineLocation, $mark));

            Log::info('Создана метка обнаружения в руднике', [
                'mark_id' => $mark->id,
                'player_id' => $player->id,
                'player_name' => $player->name,
                'mine_location_id' => $mineLocation->id,
                'mine_location_name' => $mineLocation->name,
                'location_id' => $mineLocation->location_id,
                'duration' => $duration,
                'expires_at' => $mark->expires_at
            ]);

            return $mark;

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Ошибка при создании метки обнаружения', [
                'player_id' => $player->id,
                'mine_location_id' => $mineLocation->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Применить дебаф обнаружения к игроку при добыче ресурса (обратная совместимость)
     *
     * @param User $player Игрок, добывающий ресурс
     * @param MineLocation $mineLocation Локация рудника
     * @return MineMark|null Созданная метка или null в случае ошибки
     */
    public function applyDetectionDebuff(User $player, MineLocation $mineLocation): ?MineMark
    {
        try {
            $mark = $this->createMark($player, $mineLocation);

            // Создаем соответствующий эффект в active_effects для отображения
            if ($mark) {
                $this->createDisplayEffect($player, $mineLocation, $mark);
            }

            return $mark;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Создать эффект в active_effects для отображения в интерфейсе
     *
     * @param User $player
     * @param MineLocation $mineLocation
     * @param MineMark $mark
     * @return ActiveEffect|null
     */
    private function createDisplayEffect(User $player, MineLocation $mineLocation, MineMark $mark): ?ActiveEffect
    {
        try {
            // Удаляем старые эффекты обнаружения для этой локации
            ActiveEffect::where('effect_type', 'mine_detection')
                ->where('target_type', 'player')
                ->where('target_id', $player->id)
                ->whereJsonContains('effect_data->mine_location_id', $mineLocation->id)
                ->delete();

            // Создаем новый эффект для отображения
            $effect = ActiveEffect::create([
                'effect_type' => 'mine_detection',
                'effect_name' => 'Замечен в рудниках',
                'target_type' => 'player', // Используем сокращенное имя из morphMap
                'target_id' => $player->id,
                'duration' => self::DETECTION_DURATION,
                'starts_at' => now(),
                'ends_at' => $mark->expires_at,
                'is_active' => true,
                'effect_data' => [
                    'mine_mark_id' => $mark->id,
                    'mine_location_id' => $mineLocation->id,
                    'location_id' => $mineLocation->location_id,
                    'location_name' => $mineLocation->name,
                    'description' => "Вы были замечены при добыче ресурсов в {$mineLocation->name}. Мобы могут атаковать вас!"
                ]
            ]);

            Log::info('Создан display effect для mine detection', [
                'player_id' => $player->id,
                'mine_mark_id' => $mark->id,
                'effect_id' => $effect->id,
                'mine_location' => $mineLocation->name
            ]);

            return $effect;
        } catch (\Exception $e) {
            Log::error('Ошибка создания display effect для mine detection', [
                'player_id' => $player->id,
                'mine_mark_id' => $mark->id,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Удалить существующие метки игрока в руднике
     *
     * @param int $playerId ID игрока
     * @param int $mineLocationId ID локации рудника
     * @return int Количество удаленных меток
     */
    public function deactivateExistingMarks(int $playerId, int $mineLocationId): int
    {
        // Получаем все метки игрока в этом руднике
        $existingMarks = MineMark::where('player_id', $playerId)
            ->where('mine_location_id', $mineLocationId)
            ->get();

        $deletedCount = 0;

        foreach ($existingMarks as $mark) {
            // Удаляем из кэша
            $mark->removeFromCache();

            // Удаляем связанные эффекты из active_effects
            ActiveEffect::where('effect_type', 'mine_detection')
                ->where('target_type', 'player')
                ->where('target_id', $playerId)
                ->whereJsonContains('effect_data->mine_mark_id', $mark->id)
                ->delete();

            // Полностью удаляем метку
            $mark->delete();
            $deletedCount++;
        }

        return $deletedCount;
    }

    /**
     * Проверить, есть ли у игрока активная метка в руднике
     *
     * @param int $playerId ID игрока
     * @param int $locationId ID основной локации
     * @param int|null $mineLocationId ID локации рудника
     * @return bool
     */
    public function hasActiveMark(int $playerId, int $locationId, ?int $mineLocationId = null): bool
    {
        try {
            // Проактивная очистка истекших меток
            $this->proactiveCleanupExpiredMarks($mineLocationId);

            $query = MineMark::where('player_id', $playerId)
                ->where('location_id', $locationId)
                ->where('is_active', true)
                ->where('expires_at', '>', now());

            if ($mineLocationId) {
                $query->where('mine_location_id', $mineLocationId);
            }

            $mark = $query->first();

            if ($mark) {
                Log::info('Найдена активная метка рудника', [
                    'player_id' => $playerId,
                    'mine_location_id' => $mineLocationId,
                    'mark_id' => $mark->id,
                    'expires_at' => $mark->expires_at,
                    'is_active' => $mark->is_active
                ]);

                // Кэшируем активную метку
                $mark->cacheInRedisWithPreciseTTL();
                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Ошибка при проверке активной метки рудника', [
                'player_id' => $playerId,
                'mine_location_id' => $mineLocationId,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Проверить, обнаружен ли игрок (обратная совместимость)
     */
    public function isPlayerDetected(int $playerId, int $locationId, ?int $mineLocationId = null): bool
    {
        return $this->hasActiveMark($playerId, $locationId, $mineLocationId);
    }

    /**
     * Получить всех замеченных игроков в руднике
     *
     * @param int $locationId ID основной локации
     * @param int|null $mineLocationId ID локации рудника
     * @return array
     */
    public function getMarkedPlayersInMine(int $locationId, ?int $mineLocationId = null): array
    {
        // Сначала пытаемся получить из Redis кэша
        if ($mineLocationId) {
            $cachedPlayers = MineMark::getActivePlayersInMineLocation($mineLocationId);
            if (!empty($cachedPlayers)) {
                return $cachedPlayers;
            }
        }

        // Если нет в кэше, получаем из БД
        $query = MineMark::with(['player', 'mineLocation'])
            ->where('location_id', $locationId)
            ->where('is_active', true)
            ->where('expires_at', '>', now());

        if ($mineLocationId) {
            $query->where('mine_location_id', $mineLocationId);
        }

        $marks = $query->get();

        $players = [];
        foreach ($marks as $mark) {
            // Кэшируем каждую метку
            $mark->cacheInRedisWithPreciseTTL();

            $players[] = [
                'id' => $mark->id,
                'player_id' => $mark->player_id,
                'player_name' => $mark->player->name ?? 'Unknown',
                'mine_location_id' => $mark->mine_location_id,
                'location_id' => $mark->location_id,
                'location_name' => $mark->location_name,
                'expires_at' => $mark->expires_at->timestamp,
                'remaining_seconds' => $mark->expires_at->diffInSeconds(now()),
                'is_active' => $mark->is_active,
                'last_attack_at' => $mark->last_attack_at?->timestamp,
                'attack_count' => $mark->attack_count ?? 0,
            ];
        }

        return $players;
    }

    /**
     * Получить всех обнаруженных игроков (обратная совместимость)
     */
    public function getDetectedPlayers(int $locationId, ?int $mineLocationId = null): array
    {
        return $this->getMarkedPlayersInMine($locationId, $mineLocationId);
    }



    /**
     * Удалить существующие дебафы обнаружения для предотвращения дубликатов
     *
     * @param int $playerId ID игрока
     * @param int $locationId ID локации
     * @return int Количество удаленных эффектов
     */
    private function removeExistingDetectionEffects(int $playerId, int $locationId): int
    {
        return ActiveEffect::where('effect_type', 'detection_debuff')
            ->where('target_type', 'player')
            ->where('target_id', $playerId)
            ->whereJsonContains('effect_data->location_id', $locationId)
            ->delete();
    }

    /**
     * Обновить время последней атаки для метки
     *
     * @param int $playerId ID игрока
     * @param int $mineLocationId ID локации рудника
     * @return bool
     */
    public function updateLastAttack(int $playerId, int $mineLocationId): bool
    {
        $mark = $this->getActiveMark($playerId, $mineLocationId);

        if ($mark) {
            return $mark->updateLastAttack();
        }

        return false;
    }

    /**
     * Получить активную метку игрока в руднике
     *
     * @param int $playerId ID игрока
     * @param int $mineLocationId ID локации рудника
     * @return MineMark|null
     */
    public function getActiveMark(int $playerId, int $mineLocationId): ?MineMark
    {
        return MineMark::where('player_id', $playerId)
            ->where('mine_location_id', $mineLocationId)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * Очистить истекшие метки обнаружения
     *
     * @return int Количество удаленных меток
     */
    public function cleanupExpiredMarks(): int
    {
        $count = MineMark::cleanupExpired();

        Log::info('Очищены истекшие метки рудников', [
            'cleaned_count' => $count
        ]);

        return $count;
    }

    /**
     * Очистить истекшие дебафы обнаружения (обратная совместимость)
     */
    public function cleanupExpiredDetectionEffects(): int
    {
        return $this->cleanupExpiredMarks();
    }

    /**
     * Проактивная очистка истекших меток в руднике
     *
     * @param int|null $mineLocationId ID локации рудника
     * @return int Количество очищенных меток
     */
    public function proactiveCleanupExpiredMarks(?int $mineLocationId = null): int
    {
        try {
            $query = MineMark::where(function ($query) {
                $query->where('expires_at', '<', now())
                    ->orWhere('is_active', false);
            });

            if ($mineLocationId) {
                $query->where('mine_location_id', $mineLocationId);
            }

            $expiredMarks = $query->get();

            $cleanedCount = 0;
            foreach ($expiredMarks as $mark) {
                // Удаляем из кэша
                $mark->removeFromCache();

                // Полностью удаляем метку
                $mark->delete();
                $cleanedCount++;
            }

            if ($cleanedCount > 0) {
                Log::info('Проактивная очистка истекших меток рудников', [
                    'mine_location_id' => $mineLocationId,
                    'cleaned_count' => $cleanedCount
                ]);
            }

            return $cleanedCount;
        } catch (\Exception $e) {
            Log::error('Ошибка при проактивной очистке меток рудников', [
                'mine_location_id' => $mineLocationId,
                'error' => $e->getMessage()
            ]);

            return 0;
        }
    }

    /**
     * Удалить дебаф обнаружения с игрока
     *
     * @param int $playerId ID игрока
     * @param int|null $locationId ID локации (опционально)
     * @return int Количество удаленных эффектов
     */
    public function removeDetectionDebuff(int $playerId, ?int $locationId = null): int
    {
        $query = MineMark::where('player_id', $playerId);

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        $marks = $query->get();
        $deletedCount = 0;

        foreach ($marks as $mark) {
            $mark->removeFromCache();
            $mark->delete();
            $deletedCount++;
        }

        if ($deletedCount > 0) {
            Log::info('Удалены метки обнаружения', [
                'player_id' => $playerId,
                'location_id' => $locationId,
                'deleted_count' => $deletedCount
            ]);
        }

        return $deletedCount;
    }

    /**
     * Получить статистику дебафов обнаружения по локациям
     *
     * @return array
     */
    public function getDetectionStatistics(): array
    {
        $effects = ActiveEffect::where('effect_type', 'detection_debuff')
            ->where('ends_at', '>', now())
            ->get();

        $stats = [];
        foreach ($effects as $effect) {
            $locationId = $effect->effect_data['location_id'] ?? 'unknown';
            $mineLocationId = $effect->effect_data['mine_location_id'] ?? 'unknown';
            $locationName = $effect->effect_data['location_name'] ?? 'Unknown';

            $key = "{$locationName} (ID: {$locationId}/{$mineLocationId})";

            if (!isset($stats[$key])) {
                $stats[$key] = [
                    'location_id' => $locationId,
                    'mine_location_id' => $mineLocationId,
                    'location_name' => $locationName,
                    'detected_players' => 0,
                    'avg_remaining_time' => 0
                ];
            }

            $stats[$key]['detected_players']++;
            $stats[$key]['remaining_times'][] = $effect->ends_at->diffInSeconds(now());
        }

        // Вычисляем среднее время
        foreach ($stats as &$stat) {
            if (isset($stat['remaining_times'])) {
                $stat['avg_remaining_time'] = round(array_sum($stat['remaining_times']) / count($stat['remaining_times']));
                unset($stat['remaining_times']);
            }
        }

        return array_values($stats);
    }

    /**
     * Проверить, может ли игрок быть атакован мобами (имеет дебаф обнаружения)
     *
     * @param User $player Игрок
     * @param int $locationId ID локации
     * @param int|null $mineLocationId ID подлокации рудника
     * @return bool
     */
    public function canPlayerBeAttackedByMobs(User $player, int $locationId, ?int $mineLocationId = null): bool
    {
        return $this->isPlayerDetected($player->id, $locationId, $mineLocationId);
    }

    /**
     * Получить время до окончания дебафа обнаружения
     *
     * @param int $playerId ID игрока  
     * @param int $locationId ID локации
     * @return int|null Секунды до окончания или null если дебафа нет
     */
    public function getDetectionTimeRemaining(int $playerId, int $locationId): ?int
    {
        $effect = ActiveEffect::where('effect_type', 'detection_debuff')
            ->where('target_type', 'player')
            ->where('target_id', $playerId)
            ->whereJsonContains('effect_data->location_id', $locationId)
            ->where('ends_at', '>', now())
            ->first();

        return $effect ? $effect->ends_at->diffInSeconds(now()) : null;
    }
}