<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\Location;
use App\Models\Mob;
use App\Models\MineLocation;
use App\Services\MineDetectionService;

// Загружаем Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 ПРОВЕРКА СТАТУСА ИГРОКА ADMIN В РУДНИКЕ\n";
echo "==========================================\n\n";

// 1. Находим пользователя admin
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден\n";
    exit(1);
}

echo "✅ Пользователь: {$user->name} (ID: {$user->id})\n";

// 2. Проверяем текущую локацию игрока
if ($user->statistics) {
    echo "📍 Текущая локация игрока: {$user->statistics->current_location}\n";
} else {
    echo "⚠️ У игрока нет статистики\n";
}

// 3. Проверяем активные метки в рудниках
echo "\n🎯 ПРОВЕРКА АКТИВНЫХ МЕТОК В РУДНИКАХ:\n";
$marks = MineMark::where('player_id', $user->id)
    ->where('is_active', true)
    ->where('expires_at', '>', now())
    ->get();

echo "Активных меток: {$marks->count()}\n";
foreach ($marks as $mark) {
    echo "- Метка ID: {$mark->id}\n";
    echo "  Локация: {$mark->location_name}\n";
    echo "  Mine Location ID: {$mark->mine_location_id}\n";
    echo "  Истекает: {$mark->expires_at}\n";
    echo "  Атак: {$mark->attack_count}\n";
    echo "  Последняя атака: " . ($mark->last_attack_at ?? 'Никогда') . "\n\n";
}

// 4. Проверяем локацию "аааааааааааа"
echo "🗺️ ПРОВЕРКА ЛОКАЦИИ 'аааааааааааа':\n";
$location = Location::where('name', 'аааааааааааа')->first();
if (!$location) {
    echo "❌ Локация 'аааааааааааа' не найдена\n";
} else {
    echo "✅ Локация найдена: {$location->name} (ID: {$location->id})\n";
    
    // Проверяем мобов в этой локации
    $mobs = Mob::where('location_id', $location->id)->where('hp', '>', 0)->get();
    echo "Живых мобов в локации: {$mobs->count()}\n";
    foreach ($mobs as $mob) {
        echo "- {$mob->name} (ID: {$mob->id}), HP: {$mob->hp}/{$mob->max_hp}\n";
    }
    
    // Проверяем рудники в этой локации
    $mineLocations = MineLocation::where('location_id', $location->id)->where('is_active', true)->get();
    echo "\nАктивных рудников в локации: {$mineLocations->count()}\n";
    foreach ($mineLocations as $mineLocation) {
        echo "- {$mineLocation->name} (ID: {$mineLocation->id})\n";
        
        // Проверяем, есть ли метки в этом руднике
        $mineMarks = MineMark::where('mine_location_id', $mineLocation->id)
            ->where('is_active', true)
            ->where('expires_at', '>', now())
            ->get();
        echo "  Активных меток в руднике: {$mineMarks->count()}\n";
    }
}

// 5. Проверяем сервис обнаружения
echo "\n🔧 ПРОВЕРКА СЕРВИСА ОБНАРУЖЕНИЯ:\n";
$mineDetectionService = app(MineDetectionService::class);

if ($location) {
    $markedPlayers = $mineDetectionService->getMarkedPlayersInMine($location->id);
    echo "Замеченных игроков в локации {$location->name}: " . count($markedPlayers) . "\n";
    
    foreach ($markedPlayers as $playerData) {
        echo "- Игрок: {$playerData['player_name']} (ID: {$playerData['player_id']})\n";
        echo "  Рудник: {$playerData['location_name']}\n";
        echo "  Истекает через: {$playerData['remaining_seconds']} секунд\n";
        echo "  Атак: {$playerData['attack_count']}\n\n";
    }
}

// 6. Создаем тестовую метку если её нет
if ($marks->count() === 0 && $location) {
    echo "🎯 СОЗДАНИЕ ТЕСТОВОЙ МЕТКИ:\n";
    $mineLocation = MineLocation::where('location_id', $location->id)->where('is_active', true)->first();
    
    if ($mineLocation) {
        $testMark = MineMark::create([
            'player_id' => $user->id,
            'mine_location_id' => $mineLocation->id,
            'location_id' => $location->id,
            'location_name' => $mineLocation->name,
            'expires_at' => now()->addMinutes(10),
            'is_active' => true,
            'attack_count' => 0,
            'last_attack_at' => null
        ]);
        
        echo "✅ Создана тестовая метка ID: {$testMark->id}\n";
        echo "⏰ Истекает: {$testMark->expires_at}\n";
        echo "📍 Рудник: {$testMark->location_name}\n";
    } else {
        echo "❌ Не найден активный рудник в локации {$location->name}\n";
    }
}

echo "\n✅ Проверка завершена!\n";
