<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Добавление полей для индивидуальных настроек скиллов мобов
     * Позволяет переопределять базовые параметры шаблона для конкретного моба
     */
    public function up(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Добавляем поле для индивидуального кулдауна
            if (!Schema::hasColumn('mob_skills', 'custom_cooldown')) {
                $table->integer('custom_cooldown')->nullable()->after('chance')
                    ->comment('Индивидуальный кулдаун скилла для данного моба (переопределяет значение из шаблона)');
            }

            // Добавляем поле для индивидуальной длительности эффекта
            if (!Schema::hasColumn('mob_skills', 'custom_duration')) {
                $table->integer('custom_duration')->nullable()->after('custom_cooldown')
                    ->comment('Индивидуальная длительность эффекта скилла для данного моба (переопределяет значение из шаблона)');
            }

            // Добавляем поле для отключения скилла у конкретного моба
            if (!Schema::hasColumn('mob_skills', 'is_disabled')) {
                $table->boolean('is_disabled')->default(false)->after('custom_duration')
                    ->comment('Отключен ли скилл для данного моба');
            }

            // Добавляем индексы для оптимизации запросов
            $table->index(['mob_id', 'is_disabled'], 'idx_mob_skills_mob_disabled');
            $table->index(['skill_template_id', 'is_disabled'], 'idx_mob_skills_template_disabled');
        });
    }

    /**
     * Откат миграции
     */
    public function down(): void
    {
        Schema::table('mob_skills', function (Blueprint $table) {
            // Удаляем индексы
            $table->dropIndex('idx_mob_skills_mob_disabled');
            $table->dropIndex('idx_mob_skills_template_disabled');

            // Удаляем добавленные поля
            if (Schema::hasColumn('mob_skills', 'custom_cooldown')) {
                $table->dropColumn('custom_cooldown');
            }

            if (Schema::hasColumn('mob_skills', 'custom_duration')) {
                $table->dropColumn('custom_duration');
            }

            if (Schema::hasColumn('mob_skills', 'is_disabled')) {
                $table->dropColumn('is_disabled');
            }
        });
    }
};
