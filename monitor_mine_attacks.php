<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\MineMark;
use App\Models\Location;
use App\Models\Mob;
use App\Models\MineLocation;

// Загружаем Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🔍 МОНИТОРИНГ АТАК МОБОВ В РУДНИКАХ\n";
echo "===================================\n\n";

// Находим пользователя admin
$user = User::where('name', 'admin')->first();
if (!$user) {
    echo "❌ Пользователь admin не найден\n";
    exit(1);
}

echo "👤 Мониторинг игрока: {$user->name} (ID: {$user->id})\n";

// Начальные данные
$initialMark = MineMark::where('player_id', $user->id)
    ->where('is_active', true)
    ->where('expires_at', '>', now())
    ->first();

if (!$initialMark) {
    echo "❌ Нет активных меток для мониторинга\n";
    exit(1);
}

echo "🎯 Метка найдена: ID {$initialMark->id}, Рудник: {$initialMark->location_name}\n";
echo "⚔️ Начальное количество атак: {$initialMark->attack_count}\n";
echo "⏰ Последняя атака: " . ($initialMark->last_attack_at ?? 'Никогда') . "\n\n";

echo "🕐 Начинаем мониторинг (нажмите Ctrl+C для остановки)...\n";
echo "Время\t\tАтак\tПоследняя атака\t\t\tИзменение\n";
echo "--------------------------------------------------------------------\n";

$lastAttackCount = $initialMark->attack_count;
$lastAttackTime = $initialMark->last_attack_at;

while (true) {
    sleep(5); // Проверяем каждые 5 секунд
    
    // Обновляем данные метки
    $currentMark = MineMark::find($initialMark->id);
    
    if (!$currentMark) {
        echo "❌ Метка была удалена\n";
        break;
    }
    
    $currentTime = date('H:i:s');
    $currentAttackCount = $currentMark->attack_count;
    $currentAttackTime = $currentMark->last_attack_at;
    
    // Проверяем изменения
    $attackChange = $currentAttackCount - $lastAttackCount;
    $timeChange = ($currentAttackTime != $lastAttackTime) ? '🆕' : '';
    
    // Выводим текущее состояние
    printf(
        "%s\t%d\t%s\t%s\n",
        $currentTime,
        $currentAttackCount,
        $currentAttackTime ?? 'Никогда',
        $attackChange > 0 ? "⚔️ +{$attackChange} {$timeChange}" : ''
    );
    
    // Обновляем последние значения
    $lastAttackCount = $currentAttackCount;
    $lastAttackTime = $currentAttackTime;
    
    // Проверяем, не истекла ли метка
    if ($currentMark->expires_at <= now()) {
        echo "\n⏰ Метка истекла: {$currentMark->expires_at}\n";
        break;
    }
}

echo "\n✅ Мониторинг завершен\n";
