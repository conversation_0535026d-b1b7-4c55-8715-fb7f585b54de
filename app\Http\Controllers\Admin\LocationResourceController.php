<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Location;
use App\Models\MineLocation;
use App\Models\OutpostLocation;
use App\Models\Resource;
use App\Models\SpawnedResource;
use App\Services\LocationResourceService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class LocationResourceController extends Controller
{
    protected LocationResourceService $resourceService;

    public function __construct(LocationResourceService $resourceService)
    {
        $this->resourceService = $resourceService;
    }

    /**
     * Отображение списка ресурсов локации
     */
    public function index(Request $request)
    {
        // Получаем основные локации
        $locations = Location::all();

        // Получаем подлокации рудников с их основными локациями
        $mineLocations = MineLocation::with('baseLocation')
            ->where('is_active', true)
            ->orderBy('location_id')
            ->orderBy('order')
            ->get();

        // Получаем подлокации аванпостов с их основными локациями
        $outpostLocations = OutpostLocation::with('baseLocation')
            ->where('is_active', true)
            ->orderBy('location_id')
            ->orderBy('order')
            ->get();

        // Создаем иерархический список локаций для выпадающего списка
        $hierarchicalLocations = $this->buildHierarchicalLocationsList($locations, $mineLocations, $outpostLocations);

        // Получаем выбранную локацию из параметра запроса
        $selectedLocationSelection = $request->input('location_selection', $hierarchicalLocations[0]['id'] ?? null);

        // Парсим выбор локации для определения типа и ID
        $locationData = $this->parseLocationSelection($selectedLocationSelection);

        // Получаем ресурсы в зависимости от типа выбранной локации
        $resources = $this->getResourcesForLocation($locationData);

        return view('admin.location-resources.index', [
            'hierarchicalLocations' => $hierarchicalLocations,
            'selectedLocationSelection' => $selectedLocationSelection,
            'resources' => $resources,
            'locationData' => $locationData
        ]);
    }

    /**
     * Форма создания нового ресурса
     */
    public function create()
    {
        // Получаем основные локации
        $locations = Location::all();

        // Получаем подлокации рудников с их основными локациями
        $mineLocations = MineLocation::with('baseLocation')
            ->where('is_active', true)
            ->orderBy('location_id')
            ->orderBy('order')
            ->get();

        // Получаем подлокации аванпостов с их основными локациями
        $outpostLocations = OutpostLocation::with('baseLocation')
            ->where('is_active', true)
            ->orderBy('location_id')
            ->orderBy('order')
            ->get();

        // Создаем иерархический список локаций
        $hierarchicalLocations = $this->buildHierarchicalLocationsList($locations, $mineLocations, $outpostLocations);

        $resources = Resource::all();

        return view('admin.location-resources.create', [
            'locations' => $locations,
            'hierarchicalLocations' => $hierarchicalLocations,
            'resources' => $resources
        ]);
    }

    /**
     * Сохранение нового ресурса
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'resource_id' => 'required|exists:resources,id',
            'location_selection' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    // Проверяем формат выбора локации
                    $parts = explode('_', $value);

                    // Обрабатываем специальный случай mine_base_ID
                    if (count($parts) === 3 && $parts[0] === 'mine' && $parts[1] === 'base') {
                        $type = 'mine_base';
                        $id = (int) $parts[2];
                    } elseif (count($parts) === 2) {
                        $type = $parts[0];
                        $id = (int) $parts[1];
                    } else {
                        $fail('Неверный формат выбора локации.');
                        return;
                    }

                    // Проверяем существование локации в зависимости от типа
                    switch ($type) {
                        case 'location':
                            if (!Location::where('id', $id)->exists()) {
                                $fail('Выбранная локация не существует.');
                            }
                            break;
                        case 'mine_base':
                            if (!Location::where('id', $id)->where('location_type', 'mine')->exists()) {
                                $fail('Выбранная базовая локация рудника не существует.');
                            }
                            break;
                        case 'mine':
                            if (!MineLocation::where('id', $id)->where('is_active', true)->exists()) {
                                $fail('Выбранная подлокация рудника не существует или неактивна.');
                            }
                            break;
                        case 'outpost':
                            if (!OutpostLocation::where('id', $id)->where('is_active', true)->exists()) {
                                $fail('Выбранная подлокация аванпоста не существует или неактивна.');
                            }
                            break;
                        default:
                            $fail('Неизвестный тип локации.');
                    }
                }
            ],
            'durability' => 'required|integer|min:1',
            'max_durability' => 'required|integer|min:1',
            'respawn_time' => 'required|integer|min:1',
            'spawn_chance' => 'required|integer|min:1|max:100',
            'is_active' => 'boolean'
        ]);

        // Парсим выбор локации
        $locationData = $this->parseLocationSelection($validated['location_selection']);

        // Добавляем данные о локации к валидированным данным
        $validated = array_merge($validated, $locationData);

        // Удаляем временное поле
        unset($validated['location_selection']);

        // Устанавливаем значение is_active по умолчанию, если оно не было передано
        $validated['is_active'] = $request->has('is_active') ? (bool) $request->input('is_active') : true;

        // Устанавливаем значения по умолчанию для новых полей
        $validated['respawn_time'] = $request->input('respawn_time', 60);
        $validated['spawn_chance'] = $request->input('spawn_chance', 100);

        // Логика установки времени респауна при создании
        if (!$validated['is_active']) {
            // Если ресурс неактивен, устанавливаем время респауна
            $validated['respawn_at'] = Carbon::now()->addMinutes($validated['respawn_time']);
            Log::info("Установлено время респауна при создании ресурса: {$validated['respawn_at']} (через {$validated['respawn_time']} минут)");
        } else {
            // Если ресурс активен, время респауна не устанавливаем
            $validated['respawn_at'] = null;
            Log::info("Создан активный ресурс без времени респауна");
        }

        SpawnedResource::create($validated);

        return redirect()->route('admin.location-resources.index')
            ->with('success', 'Ресурс успешно добавлен');
    }

    /**
     * Форма редактирования ресурса
     */
    public function edit($id)
    {
        $resource = SpawnedResource::findOrFail($id);
        $locations = Location::all();
        $resources = Resource::all();

        return view('admin.location-resources.edit', [
            'spawnedResource' => $resource,
            'locations' => $locations,
            'resources' => $resources
        ]);
    }

    /**
     * Обновление ресурса
     */
    public function update(Request $request, $id)
    {
        $resource = SpawnedResource::findOrFail($id);

        $validated = $request->validate([
            'resource_id' => 'required|exists:resources,id',
            'location_id' => 'required|exists:locations,id',
            'durability' => 'required|integer|min:0',
            'max_durability' => 'required|integer|min:1',
            'respawn_time' => 'required|integer|min:1',
            'spawn_chance' => 'required|integer|min:1|max:100',
            'is_active' => 'boolean',
            'respawn_at' => 'nullable|date'
        ]);

        // Устанавливаем значение is_active по умолчанию, если оно не было передано
        $validated['is_active'] = $request->has('is_active') ? (bool) $request->input('is_active') : false;

        // Устанавливаем время респавна и шанс респавна
        $validated['respawn_time'] = $request->input('respawn_time');
        $validated['spawn_chance'] = $request->input('spawn_chance');

        // Логика установки времени респауна
        if ($request->filled('respawn_at')) {
            // Если вручную указано точное время респауна, используем его
            $validated['respawn_at'] = Carbon::parse($request->input('respawn_at'));
            Log::info("Установлено точное время респауна для ресурса #{$resource->id}: {$validated['respawn_at']}");
        } else if (!$validated['is_active']) {
            // Если ресурс неактивен, рассчитываем время респауна автоматически
            $validated['respawn_at'] = Carbon::now()->addMinutes($validated['respawn_time']);
            Log::info("Рассчитано автоматическое время респауна для ресурса #{$resource->id}: {$validated['respawn_at']} (через {$validated['respawn_time']} минут)");
        } else {
            // Если ресурс активен, сбрасываем время респауна
            $validated['respawn_at'] = null;
            Log::info("Сброшено время респауна для активного ресурса #{$resource->id}");
        }

        $resource->update($validated);

        return redirect()->route('admin.location-resources.index')
            ->with('success', 'Ресурс успешно обновлен');
    }

    /**
     * Удаление ресурса
     */
    public function destroy($id)
    {
        $resource = SpawnedResource::findOrFail($id);
        $resource->delete();

        return redirect()->route('admin.location-resources.index')
            ->with('success', 'Ресурс успешно удален');
    }

    /**
     * Респаун всех ресурсов в локации
     */
    public function respawnAll(Request $request)
    {
        $locationSelection = $request->input('location_selection');

        if (!$locationSelection) {
            return redirect()->route('admin.location-resources.index')
                ->with('error', 'Не выбрана локация');
        }

        // Парсим выбор локации
        $locationData = $this->parseLocationSelection($locationSelection);

        if (!$locationData['location_id']) {
            return redirect()->route('admin.location-resources.index')
                ->with('error', 'Неверный выбор локации');
        }

        // Получаем ресурсы в зависимости от типа локации
        $query = SpawnedResource::where('is_active', false);

        switch ($locationData['type']) {
            case 'main':
                $query->where('location_id', $locationData['location_id'])
                    ->whereNull('mine_location_id');
                break;

            case 'mine_base':
                $query->where('location_id', $locationData['location_id'])
                    ->whereNull('mine_location_id');
                break;

            case 'mine':
                $query->where('mine_location_id', $locationData['mine_location_id']);
                break;

            case 'outpost':
                $query->where('location_id', $locationData['location_id'])
                    ->whereNull('mine_location_id');
                break;

            default:
                return redirect()->route('admin.location-resources.index')
                    ->with('error', 'Неподдерживаемый тип локации');
        }

        $resources = $query->get();

        foreach ($resources as $resource) {
            $resource->is_active = true;
            $resource->durability = $resource->max_durability;
            $resource->respawn_at = null;
            $resource->save();
        }

        return redirect()->route('admin.location-resources.index', ['location_selection' => $locationSelection])
            ->with('success', 'Все ресурсы в локации успешно респаунены (' . $resources->count() . ' шт.)');
    }

    /**
     * Генерация новых ресурсов для локации
     */
    public function generateResources(Request $request)
    {
        $locationSelection = $request->input('location_selection');
        $count = $request->input('count', 5);

        if (!$locationSelection) {
            return redirect()->route('admin.location-resources.index')
                ->with('error', 'Не выбрана локация');
        }

        // Парсим выбор локации
        $locationData = $this->parseLocationSelection($locationSelection);

        if (!$locationData['location_id']) {
            return redirect()->route('admin.location-resources.index')
                ->with('error', 'Неверный выбор локации');
        }

        // Генерируем ресурсы в зависимости от типа локации
        $this->generateResourcesForLocationData($locationData, $count);

        return redirect()->route('admin.location-resources.index', ['location_selection' => $locationSelection])
            ->with('success', "Сгенерировано {$count} новых ресурсов для локации: {$locationData['display_name']}");
    }

    /**
     * Удаление всех ресурсов в локации
     */
    public function deleteAll(Request $request)
    {
        $locationSelection = $request->input('location_selection');

        if (!$locationSelection) {
            return redirect()->route('admin.location-resources.index')
                ->with('error', 'Не выбрана локация');
        }

        // Парсим выбор локации
        $locationData = $this->parseLocationSelection($locationSelection);

        if (!$locationData['location_id']) {
            return redirect()->route('admin.location-resources.index')
                ->with('error', 'Неверный выбор локации');
        }

        // Удаляем ресурсы в зависимости от типа локации
        $deletedCount = $this->deleteResourcesForLocationData($locationData);

        return redirect()->route('admin.location-resources.index', ['location_selection' => $locationSelection])
            ->with('success', "Удалено {$deletedCount} ресурсов в локации: {$locationData['display_name']}");
    }

    /**
     * Построение иерархического списка локаций для выпадающего списка
     */
    private function buildHierarchicalLocationsList($locations, $mineLocations, $outpostLocations)
    {
        $hierarchicalList = [];

        foreach ($locations as $location) {
            // Добавляем основную локацию
            $hierarchicalList[] = [
                'id' => 'location_' . $location->id,
                'location_id' => $location->id,
                'mine_location_id' => null,
                'outpost_location_id' => null,
                'name' => $location->name,
                'type' => 'main',
                'display_name' => $location->name
            ];

            // Для локаций типа 'mine' добавляем их как базовые локации рудников
            if ($location->location_type === 'mine') {
                // Добавляем базовую локацию рудника как отдельный пункт для добавления ресурсов
                $hierarchicalList[] = [
                    'id' => 'mine_base_' . $location->id,
                    'location_id' => $location->id,
                    'mine_location_id' => null,
                    'outpost_location_id' => null,
                    'name' => $location->name,
                    'type' => 'mine_base',
                    'display_name' => '→ ' . $location->name . ' (базовая локация рудника)'
                ];
            }

            // Добавляем подлокации рудников для этой основной локации
            $locationMines = $mineLocations->where('location_id', $location->id);
            foreach ($locationMines as $mineLocation) {
                $hierarchicalList[] = [
                    'id' => 'mine_' . $mineLocation->id,
                    'location_id' => $location->id,
                    'mine_location_id' => $mineLocation->id,
                    'outpost_location_id' => null,
                    'name' => $mineLocation->name,
                    'type' => 'mine',
                    'display_name' => '→ ' . $location->name . ' → ' . $mineLocation->name
                ];
            }

            // Добавляем подлокации аванпостов для этой основной локации
            $locationOutposts = $outpostLocations->where('location_id', $location->id);
            foreach ($locationOutposts as $outpostLocation) {
                $hierarchicalList[] = [
                    'id' => 'outpost_' . $outpostLocation->id,
                    'location_id' => $location->id,
                    'mine_location_id' => null,
                    'outpost_location_id' => $outpostLocation->id,
                    'name' => $outpostLocation->name,
                    'type' => 'outpost',
                    'display_name' => '→ ' . $location->name . ' → ' . $outpostLocation->name
                ];
            }
        }

        return $hierarchicalList;
    }

    /**
     * Парсинг выбора локации для определения типа и ID
     */
    private function parseLocationSelection($locationSelection)
    {
        if (!$locationSelection) {
            return [
                'type' => 'main',
                'location_id' => null,
                'mine_location_id' => null,
                'outpost_location_id' => null,
                'display_name' => 'Не выбрано'
            ];
        }

        $parts = explode('_', $locationSelection);

        // Обрабатываем специальный случай mine_base_ID
        if (count($parts) === 3 && $parts[0] === 'mine' && $parts[1] === 'base') {
            $type = 'mine_base';
            $id = (int) $parts[2];
        } elseif (count($parts) === 2) {
            $type = $parts[0];
            $id = (int) $parts[1];
        } else {
            return [
                'type' => 'main',
                'location_id' => null,
                'mine_location_id' => null,
                'outpost_location_id' => null,
                'display_name' => 'Неверный формат'
            ];
        }

        switch ($type) {
            case 'location':
                $location = Location::find($id);
                return [
                    'type' => 'main',
                    'location_id' => $id,
                    'mine_location_id' => null,
                    'outpost_location_id' => null,
                    'display_name' => $location ? $location->name : 'Локация не найдена'
                ];

            case 'mine_base':
                $location = Location::find($id);
                return [
                    'type' => 'mine_base',
                    'location_id' => $id,
                    'mine_location_id' => null,
                    'outpost_location_id' => null,
                    'display_name' => $location ? $location->name . ' (базовая локация рудника)' : 'Базовая локация рудника не найдена'
                ];

            case 'mine':
                $mineLocation = MineLocation::with('baseLocation')->find($id);
                return [
                    'type' => 'mine',
                    'location_id' => $mineLocation ? $mineLocation->location_id : null,
                    'mine_location_id' => $id,
                    'outpost_location_id' => null,
                    'display_name' => $mineLocation && $mineLocation->baseLocation
                        ? $mineLocation->baseLocation->name . ' → ' . $mineLocation->name
                        : 'Подлокация рудника не найдена'
                ];

            case 'outpost':
                $outpostLocation = OutpostLocation::with('baseLocation')->find($id);
                return [
                    'type' => 'outpost',
                    'location_id' => $outpostLocation ? $outpostLocation->location_id : null,
                    'mine_location_id' => null,
                    'outpost_location_id' => $id,
                    'display_name' => $outpostLocation && $outpostLocation->baseLocation
                        ? $outpostLocation->baseLocation->name . ' → ' . $outpostLocation->name
                        : 'Подлокация аванпоста не найдена'
                ];

            default:
                return [
                    'type' => 'main',
                    'location_id' => null,
                    'mine_location_id' => null,
                    'outpost_location_id' => null,
                    'display_name' => 'Неизвестный тип локации'
                ];
        }
    }

    /**
     * Получение ресурсов для выбранной локации
     */
    private function getResourcesForLocation($locationData)
    {
        $query = SpawnedResource::with(['resource', 'location', 'mineLocation']);

        switch ($locationData['type']) {
            case 'main':
                if ($locationData['location_id']) {
                    // Для основной локации показываем только ресурсы без mine_location_id
                    $query->where('location_id', $locationData['location_id'])
                        ->whereNull('mine_location_id');
                } else {
                    // Если локация не выбрана, возвращаем пустой результат
                    $query->whereRaw('1 = 0');
                }
                break;

            case 'mine_base':
                if ($locationData['location_id']) {
                    // Для базовой локации рудника показываем ресурсы привязанные к этой локации без mine_location_id
                    $query->where('location_id', $locationData['location_id'])
                        ->whereNull('mine_location_id');
                } else {
                    $query->whereRaw('1 = 0');
                }
                break;

            case 'mine':
                if ($locationData['mine_location_id']) {
                    // Для подлокации рудника показываем только ресурсы с конкретным mine_location_id
                    $query->where('mine_location_id', $locationData['mine_location_id']);
                } else {
                    $query->whereRaw('1 = 0');
                }
                break;

            case 'outpost':
                if ($locationData['outpost_location_id']) {
                    // Для подлокации аванпоста (пока используем location_id)
                    $query->where('location_id', $locationData['location_id'])
                        ->whereNull('mine_location_id');
                } else {
                    $query->whereRaw('1 = 0');
                }
                break;

            default:
                $query->whereRaw('1 = 0');
                break;
        }

        return $query->orderBy('is_active', 'desc')
            ->orderBy('durability', 'desc')
            ->paginate(20);
    }

    /**
     * Генерация ресурсов для выбранной локации
     */
    private function generateResourcesForLocationData($locationData, $count)
    {
        $resources = Resource::all();

        if ($resources->isEmpty()) {
            return;
        }

        switch ($locationData['type']) {
            case 'main':
                // Для основной локации создаем ресурсы без mine_location_id
                foreach ($resources as $resource) {
                    for ($i = 0; $i < $count; $i++) {
                        SpawnedResource::create([
                            'resource_id' => $resource->id,
                            'location_id' => $locationData['location_id'],
                            'mine_location_id' => null,
                            'is_active' => true,
                            'durability' => rand(50, 100),
                            'max_durability' => 100,
                            'respawn_time' => rand(30, 120),
                            'spawn_chance' => rand(70, 100),
                            'respawn_at' => null
                        ]);
                    }
                }
                break;

            case 'mine_base':
                // Для базовой локации рудника создаем ресурсы без mine_location_id
                foreach ($resources as $resource) {
                    for ($i = 0; $i < $count; $i++) {
                        SpawnedResource::create([
                            'resource_id' => $resource->id,
                            'location_id' => $locationData['location_id'],
                            'mine_location_id' => null,
                            'is_active' => true,
                            'durability' => rand(50, 100),
                            'max_durability' => 100,
                            'respawn_time' => rand(30, 120),
                            'spawn_chance' => rand(70, 100),
                            'respawn_at' => null
                        ]);
                    }
                }
                break;

            case 'mine':
                // Для подлокации рудника создаем ресурсы с mine_location_id
                foreach ($resources as $resource) {
                    for ($i = 0; $i < $count; $i++) {
                        SpawnedResource::create([
                            'resource_id' => $resource->id,
                            'location_id' => $locationData['location_id'],
                            'mine_location_id' => $locationData['mine_location_id'],
                            'is_active' => true,
                            'durability' => rand(50, 100),
                            'max_durability' => 100,
                            'respawn_time' => rand(30, 120),
                            'spawn_chance' => rand(70, 100),
                            'respawn_at' => null
                        ]);
                    }
                }
                break;

            case 'outpost':
                // Для подлокации аванпоста создаем ресурсы без mine_location_id
                foreach ($resources as $resource) {
                    for ($i = 0; $i < $count; $i++) {
                        SpawnedResource::create([
                            'resource_id' => $resource->id,
                            'location_id' => $locationData['location_id'],
                            'mine_location_id' => null,
                            'is_active' => true,
                            'durability' => rand(50, 100),
                            'max_durability' => 100,
                            'respawn_time' => rand(30, 120),
                            'spawn_chance' => rand(70, 100),
                            'respawn_at' => null
                        ]);
                    }
                }
                break;
        }
    }

    /**
     * Удаление ресурсов для выбранной локации
     */
    private function deleteResourcesForLocationData($locationData)
    {
        $query = SpawnedResource::query();

        switch ($locationData['type']) {
            case 'main':
                // Для основной локации удаляем только ресурсы без mine_location_id
                $query->where('location_id', $locationData['location_id'])
                    ->whereNull('mine_location_id');
                break;

            case 'mine_base':
                // Для базовой локации рудника удаляем только ресурсы без mine_location_id
                $query->where('location_id', $locationData['location_id'])
                    ->whereNull('mine_location_id');
                break;

            case 'mine':
                // Для подлокации рудника удаляем только ресурсы с конкретным mine_location_id
                $query->where('mine_location_id', $locationData['mine_location_id']);
                break;

            case 'outpost':
                // Для подлокации аванпоста удаляем ресурсы без mine_location_id
                $query->where('location_id', $locationData['location_id'])
                    ->whereNull('mine_location_id');
                break;

            default:
                return 0;
        }

        return $query->delete();
    }


}
