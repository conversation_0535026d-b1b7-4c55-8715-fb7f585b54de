<?php

namespace App\Console\Commands;

use App\Models\SpawnedResource;
use Carbon\Carbon;
use Illuminate\Console\Command;

class MonitorResourceRespawn extends Command
{
    protected $signature = 'monitor:resource-respawn {--watch : Режим непрерывного мониторинга}';
    protected $description = 'Мониторинг системы респавна ресурсов';

    public function handle()
    {
        if ($this->option('watch')) {
            return $this->watchMode();
        }

        return $this->singleCheck();
    }

    private function singleCheck(): int
    {
        $this->info('📊 Мониторинг системы респавна ресурсов');
        $this->line('Время проверки: ' . now()->format('Y-m-d H:i:s'));
        $this->newLine();

        $this->displayStats();
        
        return Command::SUCCESS;
    }

    private function watchMode(): int
    {
        $this->info('👁️ Режим непрерывного мониторинга (Ctrl+C для выхода)');
        $this->newLine();

        while (true) {
            // Очищаем экран
            $this->output->write("\033[2J\033[H");
            
            $this->info('📊 Мониторинг системы респавна ресурсов');
            $this->line('Время проверки: ' . now()->format('Y-m-d H:i:s'));
            $this->line('Обновление каждые 30 секунд...');
            $this->newLine();

            $this->displayStats();
            
            // Ждем 30 секунд
            sleep(30);
        }

        return Command::SUCCESS;
    }

    private function displayStats(): void
    {
        // Общая статистика
        $totalResources = SpawnedResource::count();
        $activeResources = SpawnedResource::where('is_active', true)->count();
        $inactiveResources = SpawnedResource::where('is_active', false)->count();

        $this->info('🔢 Общая статистика:');
        $this->line("   Всего ресурсов: {$totalResources}");
        $this->line("   Активных: {$activeResources}");
        $this->line("   Неактивных: {$inactiveResources}");
        $this->newLine();

        // Ресурсы с проблемами
        $zeroHealthActive = SpawnedResource::where('durability', '<=', 0)
            ->where('is_active', true)
            ->count();
        
        $inactiveNoRespawn = SpawnedResource::where('is_active', false)
            ->whereNull('respawn_at')
            ->count();

        if ($zeroHealthActive > 0 || $inactiveNoRespawn > 0) {
            $this->warn('⚠️ Проблемы:');
            if ($zeroHealthActive > 0) {
                $this->line("   Активных ресурсов с 0 прочностью: {$zeroHealthActive}");
            }
            if ($inactiveNoRespawn > 0) {
                $this->line("   Неактивных без времени респавна: {$inactiveNoRespawn}");
            }
            $this->newLine();
        }

        // Ресурсы готовые к респавну
        $readyToRespawn = SpawnedResource::where('is_active', false)
            ->where('respawn_at', '<=', Carbon::now())
            ->count();

        $this->info('⏰ Статус респавна:');
        $this->line("   Готовых к респавну: {$readyToRespawn}");

        // Ресурсы ожидающие респавна
        $waitingRespawn = SpawnedResource::where('is_active', false)
            ->where('respawn_at', '>', Carbon::now())
            ->count();
        
        $this->line("   Ожидающих респавна: {$waitingRespawn}");
        $this->newLine();

        // Ближайшие респавны
        $nextRespawns = SpawnedResource::where('is_active', false)
            ->whereNotNull('respawn_at')
            ->where('respawn_at', '>', Carbon::now())
            ->orderBy('respawn_at')
            ->take(5)
            ->get(['id', 'resource_id', 'respawn_at', 'respawn_time']);

        if ($nextRespawns->count() > 0) {
            $this->info('🕐 Ближайшие респавны:');
            foreach ($nextRespawns as $resource) {
                $timeUntil = now()->diffForHumans($resource->respawn_at, true);
                $this->line("   ID {$resource->id}: через {$timeUntil} ({$resource->respawn_at->format('H:i:s')})");
            }
            $this->newLine();
        }

        // Статистика по времени респавна
        $respawnStats = SpawnedResource::where('is_active', false)
            ->whereNotNull('respawn_time')
            ->selectRaw('
                AVG(respawn_time) as avg_respawn_time,
                MIN(respawn_time) as min_respawn_time,
                MAX(respawn_time) as max_respawn_time
            ')
            ->first();

        if ($respawnStats && $respawnStats->avg_respawn_time) {
            $this->info('📈 Статистика времени респавна:');
            $this->line("   Среднее время: " . round($respawnStats->avg_respawn_time, 1) . " мин");
            $this->line("   Минимальное: {$respawnStats->min_respawn_time} мин");
            $this->line("   Максимальное: {$respawnStats->max_respawn_time} мин");
            $this->newLine();
        }

        // Рекомендации
        if ($readyToRespawn > 0) {
            $this->comment('💡 Рекомендация: Запустите "php artisan resources:respawn" для респавна готовых ресурсов');
        }

        if ($zeroHealthActive > 0 || $inactiveNoRespawn > 0) {
            $this->comment('💡 Рекомендация: Запустите "php artisan diagnose:resource-respawn --fix" для исправления проблем');
        }

        if ($readyToRespawn == 0 && $zeroHealthActive == 0 && $inactiveNoRespawn == 0) {
            $this->comment('✅ Система респавна работает корректно');
        }
    }
}
