<?php

namespace App\Services;

use App\Models\User;
use App\Models\Location;
use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Models\Skill;
use App\Models\ActiveEffect;
use App\Models\ActiveSkill;
use App\Models\PlayerSkill;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Bus;
use App\Jobs\ProcessEffectTick;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Cache;
use App\Services\LogFormattingService;

class SkillService
{
    protected $battleLogService;

    // Флаг для контроля логирования в applyBuffEffect
    private $shouldLogBuffEffects = true;

    public function __construct(BattleLogService $battleLogService)
    {
        $this->battleLogService = $battleLogService;
    }

    /**
     * Определяет локацию для ключа лога
     *
     * @param mixed $user Пользователь или объект с target_id и target_type
     * @param string $defaultLocation Локация по умолчанию
     * @return string Ключ локации для логов
     */
    private function getLocationKeyForLogs($user, $defaultLocation = 'unknown'): string
    {
        $location = $defaultLocation;

        if ($user instanceof User && isset($user->statistics->current_location)) {
            $location = $user->statistics->current_location;
        }

        // Заменяем пробелы и приводим к формату локации
        return str_replace(' ', '', $location);
    }

    // Использование умения кастером на цель
    public function useSkill($caster, $target, Skill $skill, $location)
    {
        // Проверяем кулдаун только для проверки, но не устанавливаем его здесь
        if ($caster instanceof User) {
            $skillRelation = $caster->skills()->where('skill_id', $skill->id)->first();
            if ($skillRelation && $skillRelation->cooldown_remaining > 0) {
                return ['success' => false, 'message' => "Умение '{$skill->name}' на перезарядке"];
            }
        }
        // if ($skill->cooldown > 0) {
        //     // Устанавливаем кулдаун
        //     $skillRelation = $caster->skills()->updateOrCreate(
        //         ['skill_id' => $skill->id],
        //         [
        //             'cooldown_remaining' => $skill->cooldown,
        //             'cooldown_ends_at' => now()->addSeconds($skill->cooldown)
        //         ]
        //     );
        //     if ($skillRelation && $skillRelation->cooldown_remaining > 0) {
        //         return ['success' => false, 'message' => "Умение '{$skill->name}' на перезарядке"];
        //     }
        // }

        // Если умение предназначено только для использования на себя, автоматически устанавливаем целью кастера
        if ($skill->target_type === 'self') {
            $target = $caster;
        }

        // Проверяем валидность цели
        $validationResult = $this->validateTarget($caster, $target, $skill, $location);
        if (!$validationResult['success']) {
            \Log::warning('Неверная цель для умения', [
                'skill_name' => $skill->name,
                'skill_id' => $skill->id,
                'target_type_required' => $skill->target_type,
                'caster_id' => $caster->id,
                'target_id' => $target ? $target->id : null,
                'message' => $validationResult['message']
            ]);
            return $validationResult;
        }

        // Нормализуем location_key - удаляем пробелы
        $locationKey = str_replace(' ', '', $location);

        // Если локация отсутствует или неизвестна, используем ЭльфийскаяГавань
        if (empty($locationKey) || $locationKey === 'unknown' || $locationKey === 'Неизвестныйаванпост') {
            $locationKey = 'ЭльфийскаяГавань';
            $location = 'Эльфийская Гавань';
        }

        $battleLogKey = "location:{$locationKey}:" . ($caster instanceof User ? $caster->id : $target->id);

        \Log::info('SkillService::useSkill - использование умения', [
            'caster_id' => $caster->id,
            'skill_id' => $skill->id,
            'location' => $location,
            'locationKey' => $locationKey,
            'battleLogKey' => $battleLogKey
        ]);

        // Определяем типы для morphMap
        $targetType = $target instanceof User ? 'player' : 'mob';
        $casterType = $caster instanceof User ? 'player' : 'mob';

        if ($skill->id == 10) { // Сильный удар (стан)
            // Создаем эффект стана
            ActiveEffect::create([
                'target_type' => $targetType,
                'target_id' => $target->id,
                'skill_id' => $skill->id,
                'caster_type' => $casterType,
                'caster_id' => $caster->id,
                'duration' => 10, // 10 секунд стана
                'effect_data' => ['type' => 'stun']
            ]);

            $this->battleLogService->addLog(
                $battleLogKey,
                "⚡ {$caster->name} оглушил {$target->name} на 10 секунд!",
                'danger'
            );

            return ['success' => true];
        }

        if ($skill->type === 'debuff') {
            // Проверяем, есть ли уже активный эффект с таким же скиллом на этой цели
            $existingEffect = ActiveEffect::where('target_id', $target->id)
                ->where('target_type', $targetType)
                ->where('skill_id', $skill->id)
                ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                ->first();

            if ($existingEffect) {
                \Log::info("Попытка повторного наложения активного эффекта", [
                    'skill' => $skill->name,
                    'target_id' => $target->id,
                    'target_type' => $targetType,
                    'existing_effect_id' => $existingEffect->id,
                    'remaining_duration' => $existingEffect->remaining_duration
                ]);

                return ['success' => false, 'message' => "Эффект '{$skill->name}' уже активен на цели"];
            }

            // Проверяем, есть ли effect_data в умении, иначе задаём значение по умолчанию
            $effectData = $skill->effect_data ?? ['damage' => 5]; // Значение по умолчанию для "Ядовитого облака"

            // Накладываем эффект
            $effect = ActiveEffect::create([
                'target_id' => $target->id,
                'target_type' => $targetType,
                'skill_id' => $skill->id,
                'duration' => $skill->duration, // Полная длительность действия
                'caster_id' => $caster->id,
                'caster_type' => $casterType,
                'effect_data' => $effectData, // Передаём effect_data
            ]);

            \Log::info("Наложен дебафф", [
                'skill' => $skill->name,
                'target_id' => $target->id,
                'target_type' => $targetType,
                'caster_id' => $caster->id,
                'caster_type' => $casterType,
                'duration' => $skill->duration,
                'effect_data' => $effectData,
            ]);

            // Формируем более информативное сообщение с указанием урона
            $damage = $effectData['damage'] ?? 5;
            $logMessage = "🎯 {$caster->name} наложил '{$skill->name}' на {$target->name}. Урон: {$damage} в течение {$skill->duration} сек.";
            app(BattleLogService::class)->addLog($battleLogKey, $logMessage, 'info');
        } elseif ($skill->type === 'buff') {
            // Обработка умений усиления (buff)

            // Проверяем, есть ли уже активный эффект с таким же скиллом на этой цели
            $existingEffect = ActiveEffect::where('target_id', $target->id)
                ->where('target_type', $targetType)
                ->where('skill_id', $skill->id)
                ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                ->first();

            if ($existingEffect) {
                \Log::info("Попытка повторного наложения бафф-эффекта", [
                    'skill' => $skill->name,
                    'target_id' => $target->id,
                    'target_type' => $targetType,
                    'existing_effect_id' => $existingEffect->id,
                    'remaining_duration' => $existingEffect->remaining_duration
                ]);

                return ['success' => false, 'message' => "Усиление '{$skill->name}' уже активно на цели"];
            }

            // Получаем данные эффекта из умения или используем значения по умолчанию
            $effectData = $skill->effect_data ?? ['strength' => 5];

            \Log::info("Подготовка к созданию баффа", [
                'skill_name' => $skill->name,
                'target_id' => $target->id,
                'target_type' => $targetType,
                'duration' => $skill->duration,
                'effect_data' => $effectData,
            ]);

            // Создаем активный эффект
            $effect = ActiveEffect::create([
                'target_id' => $target->id,
                'target_type' => $targetType,
                'skill_id' => $skill->id,
                'duration' => $skill->duration, // Длительность из БД
                'caster_id' => $caster->id,
                'caster_type' => $casterType,
                'effect_data' => $effectData, // Данные эффекта
            ]);

            \Log::info("Создан бафф", [
                'effect_id' => $effect->id,
                'skill_name' => $skill->name,
                'target_id' => $target->id,
                'target_type' => $targetType,
                'caster_id' => $caster->id,
                'caster_type' => $casterType,
                'duration' => $skill->duration,
                'effect_data' => $effectData,
            ]);

            // Применяем бафф
            $this->applyBuffEffect($target, $effect);

            // Формируем сообщение для лога - логируем только если shouldLogBuffEffects = true
            if ($this->shouldLogBuffEffects) {
                $strengthBonus = $effectData['strength'] ?? (isset($effectData['value']) ? $effectData['value'] : 5);
                $logMessage = "💪 {$caster->name} активировал '{$skill->name}' на {$target->name}. Бонус силы: +{$strengthBonus} на {$skill->duration} сек.";
                app(BattleLogService::class)->addLog($battleLogKey, $logMessage, 'success');
            }
        }

        // Устанавливаем кулдаун
        // $skillRelation = $caster->skills()->updateOrCreate(
        //     ['skill_id' => $skill->id],
        //     ['cooldown_remaining' => $skill->cooldown]
        // );
// Устанавливаем кулдаун ТОЛЬКО для игрока, для мобов это делает контроллер
        if ($caster instanceof User && $skill->cooldown > 0) {
            $skillRelation = $caster->skills()->updateOrCreate(
                ['skill_id' => $skill->id],
                [
                    'cooldown_remaining' => $skill->cooldown,
                    'cooldown_ends_at' => now()->addSeconds($skill->cooldown)
                ]
            );

            \Log::info("Установлен кулдаун для игрока", [
                'user_id' => $caster->id,
                'skill_id' => $skill->id,
                'skill_name' => $skill->name,
                'cooldown' => $skill->cooldown,
                'cooldown_ends_at' => now()->addSeconds($skill->cooldown)
            ]);
        }
        return ['success' => true, 'message' => "Умение '{$skill->name}' успешно использовано"];
    }

    // Проверка корректности цели для умения
    private function validateTarget($caster, $target, Skill $skill, $location)
    {
        // Обрабатываем массовые умения
        if ($skill->target_type === 'all_allies' || $skill->target_type === 'all_enemies') {
            if (!$location) {
                return ['success' => false, 'message' => 'Для массовых умений нужна локация!'];
            }
            return ['success' => true];
        }

        // Проверяем одиночные цели
        if (!$target) {
            return ['success' => false, 'message' => 'Цель не указана!'];
        }

        $casterType = $caster instanceof User ? 'user' : 'mob';
        $targetType = $target instanceof User ? 'user' : 'mob';

        // Проверка на умение для "себя"
        if ($skill->target_type === 'self' && ($casterType !== $targetType || $caster->id !== $target->id)) {
            return ['success' => false, 'message' => 'Умение можно использовать только на себя!'];
        }

        // Проверка на "союзник"
        if ($skill->target_type === 'ally') {
            if ($casterType === 'user' && $targetType === 'user') {
                // В будущем проверим, если цель в той же группе
                return ['success' => false, 'message' => 'Система групп еще не реализована!'];
            } elseif ($casterType === 'mob' && $targetType === 'mob') {
                // Проверяем, если моб в той же локации
                if ($caster->location !== $target->location) {
                    return ['success' => false, 'message' => 'Цель не в той же локации!'];
                }
            } else {
                return ['success' => false, 'message' => 'Недопустимая цель для союзного умения!'];
            }
        }

        // Проверка на "враг"
        if ($skill->target_type === 'enemy') {
            if ($casterType === 'user' && $targetType === 'user') {
                // Проверяем, если игрок — враг (разные расы)
                if ($caster->profile->race === $target->profile->race) {
                    return ['success' => false, 'message' => 'Цель не является врагом!'];
                }
            } elseif ($casterType === 'mob' && $targetType === 'user') {
                // Мобы всегда могут атаковать игроков как врагов
            } else {
                return ['success' => false, 'message' => 'Недопустимая цель для вражеского умения!'];
            }
        }

        // Проверяем, не в стане ли цель
        if ($caster instanceof User) {
            if ($caster->isStunned()) {
                return ['success' => false, 'message' => '⚡ Вы оглушены и не можете действовать!'];
            }
        }

        return ['success' => true];
    }

    // Применение умения с уроном
    private function applyDamageSkill($caster, $target, Skill $skill)
    {
        $damage = $skill->effect_data['damage'] ?? 0;
        $targetType = $target instanceof User ? 'user' : 'mob';
        $locationKey = $this->getLocationKeyForLogs($caster);
        $battleLogKey = "location:{$locationKey}:{$caster->id}";

        if ($targetType === 'user') {
            // Пересчитываем актуальный HP с учетом регена
            $actualHp = $target->profile->current_hp;
            $newHp = max(0, $actualHp - $damage);
            $target->profile->current_hp = $newHp;
            $target->profile->last_regeneration_at = now();
            $target->profile->save();
            $this->battleLogService->addLog(
                $battleLogKey,
                "Вы нанесли {$damage} урона игроку {$target->name} с помощью {$skill->name}!",
                'success'
            );
        } else {
            $target->hp = max(0, $target->hp - $damage);
            $target->save();
            $this->battleLogService->addLog(
                $battleLogKey,
                "Вы нанесли {$damage} урона мобу {$target->name} с помощью {$skill->name}!",
                'success'
            );
        }
    }

    // Применение баффа или дебаффа
    private function applyBuffDebuffSkill($caster, $target, Skill $skill)
    {
        if (!$target) {
            \Log::error('Target is null in applyBuffDebuffSkill', [
                'caster' => $caster->id,
                'skill' => $skill->id,
            ]);
            return;
        }

        // Определяем типы для morphMap
        $targetType = $target instanceof User ? 'player' : 'mob';
        $casterType = $caster instanceof User ? 'player' : 'mob';

        // Создаем эффект стана
        $effect = ActiveEffect::create([
            'target_type' => $targetType,
            'target_id' => $target->id,
            'skill_id' => $skill->id,
            'caster_type' => $casterType,
            'caster_id' => $caster->id,
            'duration' => $skill->id == 10 ? 10 : $skill->duration, // 10 секунд для стана
            'effect_data' => $skill->effect_data,
        ]);

        $locationKey = $this->getLocationKeyForLogs($target, $this->getLocationKeyForLogs($caster));
        $battleLogKey = "location:{$locationKey}:" . ($target instanceof User ? $target->id : $caster->id);
        $targetName = $target instanceof User ? $target->name : $target->name;

        // Специальное сообщение для стана
        if ($skill->id == 10) {
            $this->battleLogService->addLog(
                $battleLogKey,
                "⚡ {$targetName} оглушен на 10 секунд!",
                'danger'
            );
        } else {
            $this->battleLogService->addLog(
                $battleLogKey,
                "Применено {$skill->name} на {$targetName} на {$skill->duration} секунд!",
                $skill->type === 'buff' ? 'success' : 'danger'
            );
        }
    }

    // Применение массового умения
    private function applyMassSkill($caster, Skill $skill, $location)
    {
        $targets = [];
        if ($skill->target_type === 'all_allies') {
            $targets = Mob::where('location', $location)
                ->where('hp', '>', 0)
                ->get();
        } elseif ($skill->target_type === 'all_enemies') {
            $targets = User::whereHas('statistics', function ($q) use ($location) {
                $q->where('current_location', $location);
            })->get();
        }

        if ($targets->isEmpty()) {
            \Log::info('No targets found for mass skill', [
                'skill' => $skill->name,
                'location' => $location,
                'target_type' => $skill->target_type,
            ]);
            return;
        }

        foreach ($targets as $target) {
            if (!$target) {
                \Log::error('Invalid target in applyMassSkill', [
                    'skill' => $skill->id,
                    'location' => $location,
                ]);
                continue;
            }
            $this->applyBuffDebuffSkill($caster, $target, $skill);
        }
    }

    /**
     * Устанавливает флаг логирования эффектов баффов
     * @param bool $shouldLog Нужно ли логировать применение баффов
     */
    public function setShouldLogBuffEffects(bool $shouldLog): void
    {
        $this->shouldLogBuffEffects = $shouldLog;
    }

    /**
     * Применяет эффект баффа к цели
     *
     * @param mixed $target Цель (User или Mob)
     * @param ActiveEffect $effect Активный эффект
     * @return void
     */
    public function applyBuffEffect($target, ActiveEffect $effect)
    {
        try {
            if (!$target || !$effect || !$effect->skill) {
                \Log::warning('Cannot apply buff effect - invalid parameters');
                return;
            }

            // Проверка, был ли уже применен эффект
            $buffAppliedKey = "effect:{$effect->id}:buff_applied";
            $buffApplied = Redis::get($buffAppliedKey);

            if ($buffApplied) {
                \Log::info('Buff already applied, skipping');
                return;
            }

            $effectData = $effect->effect_data;

            // Универсальная обработка баффов характеристик для игроков
            if (isset($effectData['attribute']) && $target instanceof User) {
                $attribute = $effectData['attribute'];
                $value = $effectData['value'] ?? 0;

                // Проверяем, что характеристика существует в профиле пользователя
                if (!isset($target->profile->{$attribute})) {
                    \Log::warning("Неизвестная характеристика: {$attribute}");
                    return;
                }

                // Сохраняем оригинальное значение характеристики
                $originalValueKey = "effect:{$effect->id}:original_{$attribute}";

                if (!Redis::exists($originalValueKey)) {
                    $originalValue = $target->profile->{$attribute};
                    Redis::set($originalValueKey, $originalValue);
                    Redis::expire($originalValueKey, $effect->duration + 60); // Увеличиваем TTL для безопасности

                    \Log::info("Сохранено оригинальное значение {$attribute}", [
                        'user_id' => $target->id,
                        'effect_id' => $effect->id,
                        'attribute' => $attribute,
                        'original_value' => $originalValue,
                        'redis_key' => $originalValueKey,
                        'ttl' => $effect->duration + 60
                    ]);
                }

                // Применяем усиление характеристики
                $target->profile->{$attribute} += $value;
                $target->profile->save();

                // Устанавливаем флаг, что бафф был применен
                Redis::set($buffAppliedKey, 1);
                Redis::expire($buffAppliedKey, $effect->duration + 60);

                // Логируем применение баффа
                if ($this->shouldLogBuffEffects) {
                    $battleLogKey = $this->battleLogService->getBattleLogKey($target->id);

                    // Формируем сообщение в зависимости от типа характеристики
                    $attributeNames = [
                        'strength' => 'силы',
                        'intelligence' => 'интеллекта',
                        'dexterity' => 'ловкости',
                        'vitality' => 'выносливости',
                        'armor' => 'брони'
                    ];

                    $attributeName = $attributeNames[$attribute] ?? $attribute;
                    $emoji = $attribute === 'strength' ? '💪' : '✨';

                    $this->battleLogService->addLog(
                        $battleLogKey,
                        "{$emoji} {$effect->skill->name} активирован! Бонус {$attributeName}: +{$value} на {$effect->duration} сек.",
                        'success'
                    );
                }

                \Log::info("Применен бафф {$attribute}", [
                    'user_id' => $target->id,
                    'effect_id' => $effect->id,
                    'attribute' => $attribute,
                    'bonus_value' => $value,
                    'new_value' => $target->profile->{$attribute}
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Ошибка при применении баффа', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Удаляет эффект баффа с цели и восстанавливает характеристики
     */
    public function removeBuffEffect($target, ActiveEffect $effect)
    {
        try {
            // Проверка, существует ли эффект
            if (!$effect->exists) {
                \Log::warning('Попытка удалить несуществующий эффект', [
                    'effect_id' => $effect->id
                ]);
                return;
            }

            // Уникальный ключ для контроля повторной обработки
            $cacheKey = "effect_log_processed:{$effect->id}";
            $wasLogged = Cache::has($cacheKey);

            \Log::info('Начало удаления эффекта', [
                'effect_id' => $effect->id,
                'skill_name' => $effect->skill ? $effect->skill->name : 'неизвестно',
                'target_id' => $target->id,
                'was_logged' => $wasLogged
            ]);

            // Универсальное восстановление характеристик
            if (
                isset($effect->effect_data['attribute']) &&
                $target instanceof User
            ) {
                $attribute = $effect->effect_data['attribute'];

                // Получаем ключ Redis с оригинальным значением
                $originalValueKey = "effect:{$effect->id}:original_{$attribute}";
                $originalValue = \Illuminate\Support\Facades\Redis::get($originalValueKey);

                \Log::info("Восстановление {$attribute} из Redis", [
                    'effect_id' => $effect->id,
                    'attribute' => $attribute,
                    'original_value_key' => $originalValueKey,
                    'original_value' => $originalValue,
                    'current_value' => $target->profile->{$attribute} ?? 'не найдено'
                ]);

                if ($originalValue !== null && isset($target->profile->{$attribute})) {
                    // Сохраняем текущее значение для логирования
                    $currentValue = $target->profile->{$attribute};

                    // Прямое восстановление значения (а не вычитание бонуса)
                    $target->profile->{$attribute} = (int) $originalValue;
                    $result = $target->profile->save();

                    \Log::info("Результат восстановления {$attribute}", [
                        'effect_id' => $effect->id,
                        'attribute' => $attribute,
                        'previous_value' => $currentValue,
                        'new_value' => $target->profile->{$attribute},
                        'original_value' => $originalValue,
                        'save_result' => $result ? 'success' : 'failed'
                    ]);

                    // Удаляем ключ из Redis
                    \Illuminate\Support\Facades\Redis::del($originalValueKey);
                } else {
                    \Log::warning("Не найдено оригинальное значение {$attribute}", [
                        'effect_id' => $effect->id,
                        'attribute' => $attribute,
                        'redis_key' => $originalValueKey
                    ]);

                    // Попытка восстановить из базового значения пользователя (только для силы)
                    if ($attribute === 'strength') {
                        $baseStrengthKey = "user:{$target->id}:base_strength";
                        $baseStrength = \Illuminate\Support\Facades\Redis::get($baseStrengthKey);

                        if ($baseStrength !== null) {
                            $target->profile->strength = (int) $baseStrength;
                            $target->profile->save();

                            \Log::info('Восстановлена сила из базового значения пользователя', [
                                'user_id' => $target->id,
                                'base_strength' => $baseStrength
                            ]);
                        }
                    }
                }
            }

            // Нам нужно добавить запись в журнал боя только один раз при удалении эффекта
            $universalCacheKey = "effect_ended_log:{$effect->id}";

            if (!Cache::has($universalCacheKey)) {
                // Помечаем этот эффект как обработанный глобально
                Cache::put($universalCacheKey, true, 60);

                // Получаем ключ боевого лога
                $locationKey = $this->getLocationKeyForLogs($target);
                $battleLogKey = $target instanceof User ?
                    "battle_logs:{$target->id}" :
                    "location:{$locationKey}:{$target->id}";

                // Проверяем, является ли эффект баффом брони
                if (isset($effect->effect_data['attribute']) && $effect->effect_data['attribute'] === 'armor') {
                    // Для баффа брони используем кэш с особым ключом
                    $armorEffectKey = "effect_ended_armor_log:{$effect->id}";

                    // Если сообщение о завершении баффа брони ещё не было выведено
                    if (!Cache::has($armorEffectKey)) {
                        // Помечаем как обработанный
                        Cache::put($armorEffectKey, true, 60);

                        // Используем LogFormattingService для форматирования сообщения с иконкой
                        $logFormattingService = app(\App\Services\LogFormattingService::class);
                        $formattedMessage = $logFormattingService->formatEffectEnd($effect);

                        \Log::info('Добавление лога об окончании баффа брони с иконкой в боевые логи', [
                            'target_id' => $target->id,
                            'effect_id' => $effect->id,
                            'battle_log_key' => $battleLogKey
                        ]);

                        app(BattleLogService::class)->addLog($battleLogKey, $formattedMessage, 'warning');
                    } else {
                        \Log::info('Пропущен вывод лога об окончании баффа брони в addEffectEndedLog, так как он уже был выведен', [
                            'effect_id' => $effect->id
                        ]);
                    }
                } else {
                    // Для других эффектов выводим обычное текстовое сообщение
                    $skillName = $effect->skill ? $effect->skill->name : 'неизвестное умение';
                    $logMessage = "⚠️ Эффект {$skillName} закончился";

                    \Log::info('Добавление лога об окончании эффекта в боевые логи', [
                        'target_id' => $target->id,
                        'effect_id' => $effect->id,
                        'skill_name' => $skillName,
                        'battle_log_key' => $battleLogKey
                    ]);

                    app(BattleLogService::class)->addLog($battleLogKey, $logMessage, 'warning');
                }
            } else {
                \Log::info('Пропущен вывод лога об окончании эффекта, так как он уже был выведен в ProcessEffectTick', [
                    'effect_id' => $effect->id
                ]);
            }

            // Удаляем эффект из базы данных
            $effect->delete();
            \Log::info('Эффект удален из БД', ['effect_id' => $effect->id]);
        } catch (\Exception $e) {
            \Log::error('Ошибка в removeBuffEffect', [
                'error' => $e->getMessage(),
                'effect_id' => $effect->id ?? 'unknown',
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    private function getEffectStrengthBonus($effectData)
    {
        return $effectData['strength'] ??
            ($effectData['value'] ??
                ($effectData['attribute'] === 'strength' ? $effectData['value'] : 0));
    }

    private function addEffectEndedLog($target, $effect)
    {
        try {
            if ($target instanceof User) {
                $battleLogKey = $this->getBattleLogKey($target);

                // Проверяем, является ли эффект баффом брони
                if (isset($effect->effect_data['attribute']) && $effect->effect_data['attribute'] === 'armor') {
                    // Для баффа брони используем кэш с особым ключом
                    $armorEffectKey = "effect_ended_armor_log:{$effect->id}";

                    // Если сообщение о завершении баффа брони ещё не было выведено
                    if (!Cache::has($armorEffectKey)) {
                        // Помечаем как обработанный
                        Cache::put($armorEffectKey, true, 60);

                        // Используем LogFormattingService для форматирования сообщения с иконкой
                        $logFormattingService = app(\App\Services\LogFormattingService::class);
                        $formattedMessage = $logFormattingService->formatEffectEnd($effect);

                        \Log::info('Добавление лога об окончании баффа брони с иконкой в боевые логи', [
                            'target_id' => $target->id,
                            'effect_id' => $effect->id,
                            'battle_log_key' => $battleLogKey
                        ]);

                        app(BattleLogService::class)->addLog($battleLogKey, $formattedMessage, 'warning');
                    } else {
                        \Log::info('Пропущен вывод лога об окончании баффа брони в addEffectEndedLog, так как он уже был выведен', [
                            'effect_id' => $effect->id
                        ]);
                    }
                } else {
                    // Для других эффектов выводим обычное текстовое сообщение
                    $skillName = $effect->skill ? $effect->skill->name : 'неизвестное умение';
                    $logMessage = "⚠️ Эффект {$skillName} закончился";

                    \Log::info('Добавление лога об окончании эффекта в боевые логи', [
                        'target_id' => $target->id,
                        'effect_id' => $effect->id,
                        'skill_name' => $skillName,
                        'battle_log_key' => $battleLogKey
                    ]);

                    app(BattleLogService::class)->addLog($battleLogKey, $logMessage, 'warning');
                }
            }
        } catch (\Exception $e) {
            \Log::error('Ошибка при добавлении лога об окончании эффекта', [
                'error' => $e->getMessage(),
                'target_id' => $target->id ?? 'unknown'
            ]);
        }
    }

    /**
     * Получает единый ключ для боевых логов пользователя
     *
     * @param User $user Пользователь
     * @return string Ключ для боевых логов
     */
    private function getBattleLogKey($user): string
    {
        if ($user instanceof User) {
            return app(BattleLogService::class)->getBattleLogKey($user->id);
        }

        $location = $this->getLocationKeyForLogs($user);
        return "location:{$location}:{$user->id}";
    }

    /**
     * Проверяет и восстанавливает силу пользователя, если эффект истек
     *
     * @param User $user Пользователь
     * @return bool Были ли найдены и обработаны истекшие эффекты
     */
    public function checkAndRestoreUserStrength(User $user): bool
    {
        \Log::info('Проверка и восстановление силы пользователя', [
            'user_id' => $user->id,
            'current_strength' => $user->profile->strength
        ]);

        // Получаем все активные эффекты, связанные с силой
        $activeEffects = ActiveEffect::where('target_type', 'App\\Models\\User')
            ->where('target_id', $user->id)
            ->whereJsonContains('effect_data->attribute', 'strength')
            ->get();

        \Log::info('Найдены активные эффекты', [
            'user_id' => $user->id,
            'effects_count' => $activeEffects->count()
        ]);

        $foundExpired = false;

        foreach ($activeEffects as $effect) {
            // Проверяем, активен ли эффект
            $isActive = $effect->isActive();

            // \Log::info('Проверка активности эффекта', [
            //     'effect_id' => $effect->id,
            //     'skill_name' => $effect->skill->name ?? 'unknown',
            //     'created_at' => $effect->created_at->toDateTimeString(),
            //     'duration' => $effect->duration,
            //     'is_active' => $isActive ? 'да' : 'нет',
            //     'remaining_duration' => $effect->getRemainingDurationAttribute()
            // ]);

            // Если эффект не активен, обрабатываем его
            if (!$isActive) {
                $cacheKey = "effect_cleanup:{$effect->id}";

                // Проверяем, не был ли уже обработан этот эффект
                if (!Cache::has($cacheKey)) {
                    // Помечаем эффект как обрабатываемый
                    Cache::put($cacheKey, true, 60); // 1 минута TTL

                    \Log::info('Удаление истекшего эффекта силы', [
                        'effect_id' => $effect->id,
                        'skill_name' => $effect->skill->name ?? 'unknown',
                        'strength_before' => $user->profile->strength
                    ]);

                    // Вызываем removeBuffEffect для корректного снятия эффекта
                    $this->removeBuffEffect($user, $effect);

                    \Log::info('Эффект удален, состояние силы после удаления', [
                        'effect_id' => $effect->id,
                        'strength_after' => $user->profile->strength
                    ]);

                    $foundExpired = true;
                }
            }
        }

        return $foundExpired;
    }

    // Обработка тиков активных эффектов
    public function tickEffects()
    {
        // Получаем все активные эффекты
        $activeEffects = ActiveEffect::all();

        \Log::info("Effects found in tickEffects", [
            'effects_count' => $activeEffects->count(),
            'effects' => $activeEffects->pluck('id')->toArray()
        ]);

        if ($activeEffects->count() > 0) {
            // Отправляем задачу в очередь 'effects'
            ProcessEffectTick::dispatch($activeEffects)->onQueue('effects');

            \Log::info("ProcessEffectTick job dispatched for effects", [
                'count' => $activeEffects->count(),
                'effect_ids' => $activeEffects->pluck('id')->toArray()
            ]);
        }
    }

    private function handlePlayerDeath(User $player, $killer)
    {
        $player->statistics->increment('pve_losses');

        $player->statistics->current_location = 'Таверна';
        $player->statistics->save();

        $player->profile->current_hp = $player->profile->max_hp;
        $player->profile->last_regeneration_at = now();
        $player->profile->save();

        $player->current_target_id = null;
        $player->current_target_type = null;
        $player->last_attacker_id = null;
        $player->save();

        $locationKey = $this->getLocationKeyForLogs($player);
        $battleLogKey = "location:{$locationKey}:{$player->id}";
        app(BattleLogService::class)->addLog(
            $battleLogKey,
            "💀 Вы были побеждены {$killer->name} и телепортированы в Таверну",
            'danger'
        );

        \Log::info('Player Death Event', [
            'player_id' => $player->id,
            'killer_id' => $killer->id,
            'killer_type' => get_class($killer),
        ]);
    }

    /**
     * Назначает стартовое умение новому пользователю в зависимости от выбранного класса
     *
     * Каждый класс получает своё уникальное умение:
     * - Воин (warrior): Боевая Ярость
     * - Жрец (priest): Ярость Света
     * - Маг (mage): Ярость Грома
     *
     * Если умения нет в базе данных, оно будет создано.
     *
     * @param User $user Пользователь, которому назначается умение
     * @param string $class Класс персонажа
     * @return Skill|null Назначенное умение или null, если класс неизвестен
     */
    public function assignStartingSkill(User $user, string $class)
    {
        // Определяем маппинг классов к стартовым умениям
        $skillMap = [
            'warrior' => ['name' => 'Боевая Ярость', 'icon' => 'assets/skillBattleFury.png'],
            'priest' => ['name' => 'Ярость Света', 'icon' => 'assets/skillFuryOfLight.png'],
            'mage' => ['name' => 'Ярость Грома', 'icon' => 'assets/skillThunderFury.png'],
        ];

        // Проверяем, существует ли указанный класс в нашем маппинге
        if (!isset($skillMap[$class])) {
            \Log::warning('Неизвестный класс при назначении стартового умения', [
                'class' => $class,
                'user_id' => $user->id
            ]);
            return null;
        }

        // Получаем данные умения для выбранного класса
        $skillData = $skillMap[$class];

        // Ищем умение в базе данных или создаём новое, если оно не существует
        $skill = Skill::firstOrCreate(
            // Параметры для поиска
            ['name' => $skillData['name']],
            // Параметры для создания, если умение не найдено
            [
                'type' => 'buff',                             // Тип умения (бафф - усиливает характеристики)
                'target_type' => 'self',                      // Цель умения (self - применяется на себя)
                'cooldown' => 25,                             // Время перезарядки в секундах
                'duration' => 20,                             // Продолжительность эффекта в секундах
                'mana_cost' => 20,                            // Стоимость маны для использования
                'effect_data' => [                            // Данные эффекта в формате JSON
                    'attribute' => 'strength',                // Атрибут, на который влияет (сила)
                    'value' => 1000                           // Значение усиления атрибута
                ],
                'icon' => $skillData['icon'],                 // Путь к иконке умения
                'category' => $class,                         // Категория умения (по классу персонажа)
                'description' => 'Стартовое умение для ' . $class, // Описание умения
                'level_required' => 1,                        // Требуемый уровень
            ]
        );

        // Добавляем связь между пользователем и умением в таблицу user_skills
        $userSkill = $user->skills()->create([
            'skill_id' => $skill->id,
            'cooldown_remaining' => 0,                         // Умение сразу доступно для использования
        ]);

        \Log::info('Назначено стартовое умение', [
            'user_id' => $user->id,
            'skill_id' => $skill->id,
            'skill_name' => $skill->name,
            'class' => $class
        ]);

        return $skill;
    }

    /**
     * Назначает стартовое умение новому пользователю в зависимости от выбранного класса
     *
     * Каждый класс получает своё уникальное умение:
     * - Воин: "Боевой клич" - временно увеличивает силу
     * - Маг: "Магический барьер" - временно увеличивает защиту
     * - Лучник: "Меткий выстрел" - наносит повышенный урон
     *
     * Если умения нет в базе данных, оно будет создано.
     *
     * @param User $user Пользователь, которому назначается умение
     * @param string $class Класс персонажа
     * @return Skill|null Назначенное умение или null в случае ошибки
     */
    public function assignStarterSkill(User $user, string $class)
    {
        // Карта стартовых умений для разных классов
        $skillMap = [
            'warrior' => [
                'name' => 'Боевой клич',                // Имя умения
                'icon' => 'images/skills/battlecry.png'  // Изображение умения
            ],
            'mage' => [
                'name' => 'Магический барьер',
                'icon' => 'images/skills/magicbarrier.png'
            ],
            'archer' => [
                'name' => 'Меткий выстрел',
                'icon' => 'images/skills/preciseshot.png'
            ]
        ];

        // Проверяем, существует ли умение для выбранного класса
        if (!isset($skillMap[$class])) {
            // Логируем предупреждение, если класс неизвестен
            \Log::error('Невозможно назначить стартовое умение - неизвестный класс', [
                'user_id' => $user->id,
                'requested_class' => $class,
                'available_classes' => array_keys($skillMap)
            ]);
            return null;
        }

        // Получаем данные умения для выбранного класса
        $skillData = $skillMap[$class];

        // Ищем умение в базе данных или создаём новое, если оно не существует
        // firstOrCreate уменьшает количество запросов и предотвращает дублирование
        $skill = Skill::firstOrCreate(
            // Параметры для поиска
            ['name' => $skillData['name']],
            // Параметры для создания, если умение не найдено
            [
                'type' => 'buff',                             // Тип умения (бафф - усиливает характеристики)
                'target_type' => 'self',                      // Цель умения (self - применяется на себя)
                'cooldown' => 25,                             // Время перезарядки в секундах
                'duration' => 20,                             // Продолжительность эффекта в секундах
                'mana_cost' => 20,                            // Стоимость маны для использования
                'effect_data' => [                            // Данные эффекта в формате JSON
                    'attribute' => 'strength',                // Атрибут, на который влияет (сила)
                    'value' => 1000                           // Значение усиления атрибута
                ],
                'icon' => $skillData['icon'],                 // Путь к иконке умения
            ]
        );

        // Добавляем связь между пользователем и умением в таблицу user_skills
        $userSkill = $user->skills()->create([
            'skill_id' => $skill->id,
            'cooldown_remaining' => 0,                         // Умение сразу доступно для использования
        ]);

        // Логируем успешное назначение умения
        \Log::info('Назначено стартовое умение', [
            'user_id' => $user->id,
            'skill_id' => $skill->id,
            'skill_name' => $skill->name,
            'class' => $class
        ]);

        return $skill;
    }

    /**
     * Накладывает дебафф на цель
     */
    public function applyDebuff($target, $caster, $skill, $duration = null, $effectData = [])
    {
        // Получаем длительность дебаффа
        $duration = $duration ?? $skill->duration;

        // Создаем запись о дебаффе
        $activeEffect = new ActiveEffect();
        $activeEffect->skill_id = $skill->id;
        $activeEffect->target_id = $target->id;
        $activeEffect->target_type = get_class($target);
        $activeEffect->caster_id = $caster->id;
        $activeEffect->caster_type = get_class($caster);
        $activeEffect->duration = $duration;
        $activeEffect->effect_data = $effectData;
        $activeEffect->save();

        // Логируем наложение дебаффа
        \Log::info('Наложен дебафф', [
            'skill' => $skill->name,
            'target_id' => $target->id,
            'target_type' => $activeEffect->target_type === \App\Models\User::class ? 'player' : 'mob',
            'caster_id' => $caster->id,
            'caster_type' => $activeEffect->caster_type === \App\Models\User::class ? 'player' : 'mob',
            'duration' => $duration,
            'effect_data' => $effectData
        ]);

        // Получаем battleLogService для добавления сообщений
        $battleLogService = app(\App\Services\BattleLogService::class);

        // Отображаем сообщение о наложении дебаффа
        if ($skill->name === 'Ядовитое Облако' && isset($effectData['damage'])) {
            $damage = $effectData['damage'];
            $logMessage = "🎯 {$caster->name} наложил '{$skill->name}' на {$target->name}. Урон: {$damage} в течение {$duration} сек.";
            $battleLogKey = "location:tarnmore_quarry:" . ($target instanceof \App\Models\User ? $target->id : $caster->id);
            $battleLogService->addLog($battleLogKey, $logMessage, 'info');

            // НЕ наносим начальный урон при наложении дебаффа
            // Урон будет наноситься только через тики в ProcessEffectTick

            // Не устанавливаем флаг initial_damage, чтобы первый тик нанес урон
        }

        return $activeEffect;
    }

    /**
     * Получает умения пользователя с информацией о каждом умении
     *
     * @param User $user Пользователь, для которого нужно получить умения
     * @return \Illuminate\Database\Eloquent\Collection Коллекция умений пользователя
     */
    public function getUserSkills(User $user)
    {
        // Получаем умения пользователя с загрузкой связанной информации об умении
        return $user->skills()->with('skill')->get();
    }

    /**
     * Гарантированное восстановление силы пользователя до правильного значения
     */
    public function ensureCorrectStrength(User $user)
    {
        try {
            // Проверяем наличие активных эффектов силы
            $activeBuffs = ActiveEffect::where('target_type', 'App\\Models\\User')
                ->where('target_id', $user->id)
                ->whereJsonContains('effect_data->attribute', 'strength')
                ->whereRaw('EXTRACT(EPOCH FROM (NOW() - created_at)) < duration')
                ->count();

            // Если нет активных баффов, но у пользователя значение силы не соответствует базовому
            if ($activeBuffs == 0) {
                // Проверим, есть ли в кеше базовое значение силы
                $baseStrengthKey = "user:{$user->id}:base_strength";
                $baseStrength = \Illuminate\Support\Facades\Redis::get($baseStrengthKey);

                if ($baseStrength === null) {
                    // Если нет кешированного значения, берем текущее как базовое
                    \Illuminate\Support\Facades\Redis::set($baseStrengthKey, $user->profile->strength);
                    \Log::info("Сохранено базовое значение силы", [
                        'user_id' => $user->id,
                        'base_strength' => $user->profile->strength
                    ]);
                } else {
                    // Проверяем, нужно ли восстановить значение силы
                    $expectedStrength = (int) $baseStrength;

                    if ($user->profile->strength != $expectedStrength) {
                        \Log::warning("Обнаружено несоответствие силы без активных эффектов", [
                            'user_id' => $user->id,
                            'current_strength' => $user->profile->strength,
                            'expected_strength' => $expectedStrength
                        ]);

                        // Восстанавливаем корректное значение
                        $user->profile->strength = $expectedStrength;
                        $user->profile->save();

                        \Log::info("Автоматически восстановлено правильное значение силы", [
                            'user_id' => $user->id,
                            'restored_strength' => $expectedStrength
                        ]);

                        return true;
                    }
                }
            }

            return false;
        } catch (\Exception $e) {
            \Log::error('Ошибка при проверке силы пользователя', [
                'error' => $e->getMessage(),
                'user_id' => $user->id
            ]);
            return false;
        }
    }

    /**
     * Обрабатывает скиллы моба на основе шаблонов
     *
     * @param Mob $mob Моб, который использует скиллы
     * @param User|null $target Цель скилла (игрок)
     * @param array $context Контекст использования скилла
     * @return array Результат обработки скиллов
     */
    public function processMobSkillTemplates(Mob $mob, ?User $target = null, array $context = []): array
    {
        $activatedSkills = [];

        // Получаем все скиллы моба с их шаблонами
        $mobSkills = $mob->skills()->with('skillTemplate')->get();

        if ($mobSkills->isEmpty()) {
            return $activatedSkills;
        }

        foreach ($mobSkills as $mobSkill) {
            try {
                // Проверяем, есть ли шаблон скилла
                if (!$mobSkill->skillTemplate) {
                    continue;
                }

                $template = $mobSkill->skillTemplate;

                // Проверяем, активен ли шаблон
                if (!$template->is_active) {
                    continue;
                }

                // Проверяем кулдаун
                if (!$this->isMobSkillAvailable($mobSkill)) {
                    continue;
                }

                // Проверяем условия здоровья моба
                $healthPercent = ($mob->hp / $mob->max_hp) * 100;
                if ($healthPercent < $template->min_health_percent || $healthPercent > $template->max_health_percent) {
                    continue;
                }

                // Проверяем шанс срабатывания
                if (rand(1, 100) > ($mobSkill->chance ?? $template->chance)) {
                    continue;
                }

                // Активируем скилл
                $skillResult = $this->activateMobSkillTemplate($mob, $mobSkill, $template, $target, $context);

                if ($skillResult['success']) {
                    $activatedSkills[] = $skillResult;

                    // Устанавливаем кулдаун
                    $this->setMobSkillCooldown($mobSkill, $template->cooldown);

                    \Log::info('Активирован скилл моба по шаблону', [
                        'mob_id' => $mob->id,
                        'mob_name' => $mob->name,
                        'skill_template_id' => $template->id,
                        'skill_name' => $template->name,
                        'target_id' => $target?->id,
                        'context' => $context
                    ]);
                }
            } catch (\Exception $e) {
                \Log::error('Ошибка при обработке скилла моба', [
                    'mob_id' => $mob->id,
                    'mob_skill_id' => $mobSkill->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $activatedSkills;
    }

    /**
     * Проверяет, доступен ли скилл моба для использования
     */
    protected function isMobSkillAvailable(MobSkill $mobSkill): bool
    {
        if (!$mobSkill->cooldown_ends_at) {
            return true;
        }

        return now()->gte($mobSkill->cooldown_ends_at);
    }

    /**
     * Устанавливает кулдаун для скилла моба
     */
    protected function setMobSkillCooldown(MobSkill $mobSkill, int $cooldownSeconds): void
    {
        $mobSkill->update([
            'cooldown_ends_at' => now()->addSeconds($cooldownSeconds),
            'last_used_at' => now()
        ]);
    }

    /**
     * Активирует скилл моба на основе шаблона
     */
    protected function activateMobSkillTemplate(Mob $mob, MobSkill $mobSkill, MobSkillTemplate $template, ?User $target, array $context): array
    {
        try {
            // Определяем цель в зависимости от типа
            $actualTarget = $this->determineMobSkillTarget($mob, $target, $template->target_type);

            if (!$actualTarget) {
                return ['success' => false, 'message' => 'Нет подходящей цели для скилла'];
            }

            // Применяем эффект в зависимости от типа
            $result = $this->applyMobSkillEffect($mob, $actualTarget, $template, $mobSkill, $context);

            if ($result['success']) {
                // Логируем использование скилла
                $this->logMobSkillUsage($mob, $actualTarget, $template, $context);
            }

            return $result;
        } catch (\Exception $e) {
            \Log::error('Ошибка при активации скилла моба', [
                'mob_id' => $mob->id,
                'template_id' => $template->id,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => 'Ошибка при активации скилла'];
        }
    }

    /**
     * Определяет цель для скилла моба
     */
    protected function determineMobSkillTarget(Mob $mob, ?User $target, string $targetType)
    {
        switch ($targetType) {
            case 'player':
                return $target;
            case 'self':
                return $mob;
            case 'mob':
                // Можно расширить для поиска других мобов
                return $mob;
            case 'area':
                // Для области возвращаем основную цель
                return $target;
            default:
                return null;
        }
    }

    /**
     * Применяет эффект скилла моба на основе шаблона
     */
    protected function applyMobSkillEffect(Mob $mob, $target, MobSkillTemplate $template, MobSkill $mobSkill, array $context): array
    {
        try {
            switch ($template->effect_type) {
                case 'stun':
                    return $this->applyMobStunEffect($mob, $target, $template, $mobSkill, $context);
                case 'damage':
                    return $this->applyMobDamageEffect($mob, $target, $template, $context);
                case 'buff':
                    return $this->applyMobBuffEffect($mob, $target, $template, $context);
                case 'debuff':
                    return $this->applyMobDebuffEffect($mob, $target, $template, $context);
                case 'dot':
                    return $this->applyMobDotEffect($mob, $target, $template, $context);
                case 'hot':
                    return $this->applyMobHotEffect($mob, $target, $template, $context);
                case 'heal':
                    return $this->applyMobHealEffect($mob, $target, $template, $context);
                default:
                    return ['success' => false, 'message' => 'Неизвестный тип эффекта: ' . $template->effect_type];
            }
        } catch (\Exception $e) {
            \Log::error('Ошибка при применении эффекта скилла моба', [
                'mob_id' => $mob->id ?? 'unknown',
                'template_id' => $template->id ?? 'unknown',
                'effect_type' => $template->effect_type,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'message' => 'Ошибка при применении эффекта'];
        }
    }

    /**
     * Применяет эффект оглушения от моба
     */
    protected function applyMobStunEffect(Mob $mob, $target, MobSkillTemplate $template, MobSkill $mobSkill, array $context): array
    {
        if (!($target instanceof User)) {
            return ['success' => false, 'message' => 'Стан можно применить только к игроку'];
        }

        $effectData = $template->effect_data ?? [];
        $duration = $mobSkill->getEffectiveDuration();

        // Создаем эффект стана
        ActiveEffect::create([
            'target_type' => 'player',
            'target_id' => $target->id,
            'caster_type' => 'mob',
            'caster_id' => $mob->id,
            'skill_id' => null, // Используем шаблон вместо skill_id
            'duration' => $duration,
            'effect_data' => array_merge($effectData, ['type' => 'stun', 'template_id' => $template->id, 'mob_skill_id' => $mobSkill->id]),
            'ends_at' => now()->addSeconds($duration)
        ]);

        return [
            'success' => true,
            'message' => $effectData['message'] ?? "Вы оглушены на {$duration} секунд!",
            'effect_type' => 'stun',
            'duration' => $duration
        ];
    }

    /**
     * Применяет эффект урона от моба
     */
    protected function applyMobDamageEffect(Mob $mob, $target, MobSkillTemplate $template, array $context): array
    {
        if (!($target instanceof User)) {
            return ['success' => false, 'message' => 'Урон можно нанести только игроку'];
        }

        $effectData = $template->effect_data ?? [];
        $damage = $effectData['damage'] ?? 50;

        // Наносим урон
        $target->profile->hp = max(0, $target->profile->hp - $damage);
        $target->profile->save();

        return [
            'success' => true,
            'message' => $effectData['message'] ?? "Получен урон: {$damage}",
            'effect_type' => 'damage',
            'damage' => $damage
        ];
    }

    /**
     * Применяет эффект усиления от моба (на себя)
     */
    protected function applyMobBuffEffect(Mob $mob, $target, MobSkillTemplate $template, array $context): array
    {
        $effectData = $template->effect_data ?? [];
        $duration = $template->duration;

        // Создаем эффект усиления
        ActiveEffect::create([
            'target_type' => 'mob',
            'target_id' => $mob->id,
            'caster_type' => 'mob',
            'caster_id' => $mob->id,
            'skill_id' => null,
            'duration' => $duration,
            'effect_data' => array_merge($effectData, ['type' => 'buff', 'template_id' => $template->id]),
            'ends_at' => now()->addSeconds($duration)
        ]);

        return [
            'success' => true,
            'message' => $effectData['message'] ?? "Моб получил усиление на {$duration} секунд!",
            'effect_type' => 'buff',
            'duration' => $duration
        ];
    }

    /**
     * Применяет эффект ослабления от моба
     */
    protected function applyMobDebuffEffect(Mob $mob, $target, MobSkillTemplate $template, array $context): array
    {
        if (!($target instanceof User)) {
            return ['success' => false, 'message' => 'Ослабление можно применить только к игроку'];
        }

        $effectData = $template->effect_data ?? [];
        $duration = $template->duration;

        // Создаем эффект ослабления
        ActiveEffect::create([
            'target_type' => 'player',
            'target_id' => $target->id,
            'caster_type' => 'mob',
            'caster_id' => $mob->id,
            'skill_id' => null,
            'duration' => $duration,
            'effect_data' => array_merge($effectData, ['type' => 'debuff', 'template_id' => $template->id]),
            'ends_at' => now()->addSeconds($duration)
        ]);

        return [
            'success' => true,
            'message' => $effectData['message'] ?? "Вы ослаблены на {$duration} секунд!",
            'effect_type' => 'debuff',
            'duration' => $duration
        ];
    }

    /**
     * Применяет эффект урона со временем (DOT) от моба
     */
    protected function applyMobDotEffect(Mob $mob, $target, MobSkillTemplate $template, array $context): array
    {
        if (!($target instanceof User)) {
            return ['success' => false, 'message' => 'DOT можно применить только к игроку'];
        }

        $effectData = $template->effect_data ?? [];
        $duration = $template->duration;

        // Создаем эффект урона со временем
        ActiveEffect::create([
            'target_type' => 'player',
            'target_id' => $target->id,
            'caster_type' => 'mob',
            'caster_id' => $mob->id,
            'skill_id' => null,
            'duration' => $duration,
            'effect_data' => array_merge($effectData, ['type' => 'dot', 'template_id' => $template->id]),
            'ends_at' => now()->addSeconds($duration)
        ]);

        return [
            'success' => true,
            'message' => $effectData['message'] ?? "На вас наложен эффект кровотечения!",
            'effect_type' => 'dot',
            'duration' => $duration
        ];
    }

    /**
     * Применяет эффект лечения со временем (HOT) от моба
     */
    protected function applyMobHotEffect(Mob $mob, $target, MobSkillTemplate $template, array $context): array
    {
        $effectData = $template->effect_data ?? [];
        $duration = $template->duration;

        // Определяем тип цели
        $targetType = ($target instanceof User) ? 'player' : 'mob';
        $targetId = $target->id;

        // Создаем эффект лечения со временем
        ActiveEffect::create([
            'target_type' => $targetType,
            'target_id' => $targetId,
            'caster_type' => 'mob',
            'caster_id' => $mob->id,
            'skill_id' => null,
            'duration' => $duration,
            'effect_data' => array_merge($effectData, ['type' => 'hot', 'template_id' => $template->id]),
            'ends_at' => now()->addSeconds($duration)
        ]);

        return [
            'success' => true,
            'message' => $effectData['message'] ?? "Наложен эффект регенерации!",
            'effect_type' => 'hot',
            'duration' => $duration
        ];
    }

    /**
     * Применяет эффект мгновенного лечения от моба
     */
    protected function applyMobHealEffect(Mob $mob, $target, MobSkillTemplate $template, array $context): array
    {
        $effectData = $template->effect_data ?? [];
        $healAmount = $effectData['heal_amount'] ?? 100;

        if ($target instanceof User) {
            // Лечим игрока
            $target->profile->hp = min($target->profile->max_hp, $target->profile->hp + $healAmount);
            $target->profile->save();
        } elseif ($target instanceof Mob) {
            // Лечим моба
            $target->hp = min($target->max_hp, $target->hp + $healAmount);
            $target->save();
        }

        return [
            'success' => true,
            'message' => $effectData['message'] ?? "Восстановлено {$healAmount} HP!",
            'effect_type' => 'heal',
            'heal_amount' => $healAmount
        ];
    }

    /**
     * Логирует использование скилла моба
     */
    protected function logMobSkillUsage(Mob $mob, $target, MobSkillTemplate $template, array $context): void
    {
        try {
            $targetName = ($target instanceof User) ? $target->name : $target->name;
            $targetType = ($target instanceof User) ? 'игрока' : 'моба';

            // Определяем ключ для логов
            $locationKey = $context['location_key'] ?? 'unknown';
            $battleLogKey = "location:{$locationKey}:" . ($target instanceof User ? $target->id : $mob->id);

            // Формируем сообщение в зависимости от типа эффекта
            $message = $this->formatMobSkillLogMessage($mob, $target, $template);

            // Определяем тип лога
            $logType = $this->getMobSkillLogType($template->effect_type);

            // Добавляем лог
            $this->battleLogService->addLog($battleLogKey, $message, $logType);

        } catch (\Exception $e) {
            \Log::error('Ошибка при логировании скилла моба', [
                'mob_id' => $mob->id ?? 'unknown',
                'template_id' => $template->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Форматирует сообщение для лога скилла моба
     */
    protected function formatMobSkillLogMessage(Mob $mob, $target, MobSkillTemplate $template): string
    {
        $mobName = $mob->name ?? 'Неизвестный моб';
        $targetName = ($target instanceof User) ? $target->name : $target->name;
        $skillName = $template->name;

        switch ($template->effect_type) {
            case 'stun':
                return "⚡ {$mobName} оглушил {$targetName} скиллом '{$skillName}'!";
            case 'damage':
                return "💥 {$mobName} нанес урон {$targetName} скиллом '{$skillName}'!";
            case 'buff':
                return "💪 {$mobName} получил усиление от '{$skillName}'!";
            case 'debuff':
                return "💀 {$mobName} ослабил {$targetName} скиллом '{$skillName}'!";
            case 'dot':
                return "🩸 {$mobName} наложил кровотечение на {$targetName} скиллом '{$skillName}'!";
            case 'hot':
                return "💚 {$mobName} наложил регенерацию скиллом '{$skillName}'!";
            case 'heal':
                return "💚 {$mobName} восстановил здоровье скиллом '{$skillName}'!";
            default:
                return "✨ {$mobName} использовал '{$skillName}' на {$targetName}!";
        }
    }

    /**
     * Определяет тип лога для скилла моба
     */
    protected function getMobSkillLogType(string $effectType): string
    {
        switch ($effectType) {
            case 'stun':
            case 'damage':
            case 'debuff':
            case 'dot':
                return 'danger';
            case 'buff':
            case 'hot':
            case 'heal':
                return 'success';
            default:
                return 'info';
        }
    }
}