<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Models\User;
use App\Services\TargetManagementService;
use App\Services\PlayerLocationCleanupService;

class ResetUserTargetOnLocationChange
{
    /**
     * Сервис для управления таргетами
     *
     * @var TargetManagementService
     */
    protected $targetService;

    /**
     * Сервис для очистки данных игрока при смене локации
     *
     * @var PlayerLocationCleanupService
     */
    protected $cleanupService;

    /**
     * Конструктор middleware
     *
     * @param TargetManagementService $targetService
     * @param PlayerLocationCleanupService $cleanupService
     */
    public function __construct(TargetManagementService $targetService, PlayerLocationCleanupService $cleanupService)
    {
        $this->targetService = $targetService;
        $this->cleanupService = $cleanupService;
    }

    /**
     * Обрабатывает входящий запрос и сбрасывает таргеты при смене локации.
     *
     * @param Request $request Входящий запрос
     * @param Closure $next Следующий обработчик
     * @return Response Ответ
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();

            // Проверяем, оглушен ли игрок
            $isStunned = $user->isStunned();

            // Если игрок оглушен, блокируем смену локаций
            if ($isStunned && $this->isLocationChangeRequest($request)) {
                // Получаем сообщение об оглушении
                $stunMessage = $this->getStunMessage($user);

                // Для AJAX-запросов
                if ($request->ajax() || $request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'error' => 'stun_blocked',
                        'message' => $stunMessage
                    ], 403);
                }

                // Для обычных запросов - редирект назад с сообщением
                return redirect()->back()->with('error', $stunMessage);
            }

            // Получаем текущую локацию пользователя
            $currentLocation = $user->statistics->current_location ?? null;

            // Проверяем, является ли текущий запрос боевым действием
            $path = $request->path();
            $isBattleAction = false;

            // Шаблоны URL для боевых действий
            $battleActionPatterns = [
                '/\/attack$/',
                '/\/attack-any$/',
                '/\/attack_any_player$/',
                '/\/attack-mob$/',
                '/\/select-mob$/',
                '/\/select-bot$/',
                '/\/retaliate$/',
                '/\/change-target$/',
                '/\/attack_any$/',
                '/\/use-skill\/\d+$/',
                '/\/hit-resource$/'
            ];

            // Проверяем, является ли запрос действием в подлокации шахты
            $isMineSubLocationAction = strpos($path, 'battle/mines/') === 0;

            // Если это действие в кастомной шахте, проверяем, содержит ли путь боевое действие
            $isMineSubLocationBattleAction = false;
            if ($isMineSubLocationAction) {
                foreach ($battleActionPatterns as $pattern) {
                    if (preg_match($pattern, $path)) {
                        $isMineSubLocationBattleAction = true;
                        Log::info("🎯 Обнаружен шаблон боевого действия в кастомной шахте", [
                            'path' => $path,
                            'pattern' => $pattern,
                            'user_id' => $user->id
                        ]);
                        break;
                    }
                }
            }

            foreach ($battleActionPatterns as $pattern) {
                if (preg_match($pattern, $path)) {
                    $isBattleAction = true;
                    Log::info("🎯 Обнаружен шаблон боевого действия", [
                        'path' => $path,
                        'pattern' => $pattern,
                        'user_id' => $user->id
                    ]);
                    break;
                }
            }

            // Если это боевое действие или действие в подлокации шахты, не сбрасываем цель
            if ($isBattleAction || $isMineSubLocationAction) {
                // Если это боевое действие в кастомной шахте, сохраняем маршрут в сессии
                if ($isMineSubLocationBattleAction) {
                    // Получаем базовый маршрут (без боевого действия)
                    $baseRoute = preg_replace('#(/attack-mob|/select-mob|/hit-resource|/use-skill/\d+|/attack-player|/attack-any-player|/retaliate)$#', '', $path);
                    session(['last_battle_route' => $baseRoute]);
                    Log::info("🎯 Сохранен базовый маршрут кастомной шахты в сессии", [
                        'path' => $path,
                        'base_route' => $baseRoute,
                        'user_id' => $user->id
                    ]);
                }

                Log::info("🎯 Боевое действие или действие в шахте: сохраняем цель пользователя #{$user->id}", [
                    'path' => $path,
                    'is_battle_action' => $isBattleAction,
                    'is_mine_sublocation_action' => $isMineSubLocationAction,
                    'is_mine_sublocation_battle_action' => $isMineSubLocationBattleAction,
                    'current_target_type' => $user->current_target_type,
                    'current_target_id' => $user->current_target_id
                ]);

                return $next($request);
            }

            // Проверяем, является ли текущий путь частью боевого пути
            // Например, если путь содержит "battle/outposts/dawn_fort", это боевой путь
            $battlePaths = [
                'battle/outposts/dawn_fort',
                'battle/outposts/sandy_stronghold',
                'battle/outposts/elven_haven',
                'battle/mines/tarnmore_quarry'
            ];

            foreach ($battlePaths as $battlePath) {
                if (strpos($path, $battlePath) === 0) {
                    // Если это боевой путь, проверяем, есть ли у пользователя цель
                    if ($user->current_target_type && $user->current_target_id) {
                        Log::info("🎯 Боевой путь: сохраняем цель пользователя #{$user->id}", [
                            'path' => $path,
                            'battle_path' => $battlePath,
                            'current_target_type' => $user->current_target_type,
                            'current_target_id' => $user->current_target_id
                        ]);

                        // Если у пользователя есть цель, не сбрасываем её при переходе между URL в рамках одной боевой локации
                        return $next($request);
                    }
                }
            }

            // Определяем новую локацию из разных источников
            $newLocation = $this->determineNewLocation($request);

            // Если локация изменилась, сбрасываем таргеты
            if ($newLocation && $newLocation !== $currentLocation) {
                Log::info("🔄 Смена локации: сбрасываем таргет у пользователя #{$user->id}", [
                    'старое местоположение' => $currentLocation,
                    'новое местоположение' => $newLocation,
                    'url' => $request->url(),
                    'path' => $request->path()
                ]);

                // Используем сервис для сброса таргетов
                $this->targetService->resetUserTargets($user);

                // КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Очищаем данные игрока из Redis предыдущей локации
                // Это предотвращает лечение ботами из старых локаций
                $this->cleanupService->cleanupPlayerLocationData($user->id, $currentLocation, $newLocation);

                // Дополнительно сбрасываем таргеты напрямую для надежности
                $user->update([
                    'current_target_id' => null,
                    'current_target_type' => null,
                    'last_attacker_id' => null // Сбрасываем последнего атакующего при смене локации
                ]);

                // Обновляем объект пользователя
                $user->refresh();

                // Обновляем локацию в статистике
                $user->statistics->update([
                    'current_location' => $newLocation
                ]);
            }
        }

        return $next($request);
    }

    /**
     * Определяет новую локацию из разных источников в запросе
     *
     * @param Request $request Входящий запрос
     * @return string|null Название новой локации или null
     */
    private function determineNewLocation(Request $request): ?string
    {
        // Проверяем параметр маршрута
        $locationFromRoute = $request->route('location');
        if ($locationFromRoute) {
            return $locationFromRoute;
        }

        // Проверяем параметр запроса
        $locationFromInput = $request->input('location');
        if ($locationFromInput) {
            return $locationFromInput;
        }

        // Проверяем URL на наличие ключевых слов локаций
        $path = $request->path();

        // Игнорируем действия в URL, которые не должны сбрасывать цель
        // Например, при атаке цели URL меняется, но локация остается той же
        $actionPatterns = [
            '/\/attack$/',
            '/\/attack-any$/',
            '/\/attack_any_player$/',
            '/\/attack-mob$/',
            '/\/select-mob$/',
            '/\/select-bot$/',
            '/\/retaliate$/',
            '/\/change-target$/',
            '/\/attack_any$/'
        ];

        foreach ($actionPatterns as $pattern) {
            if (preg_match($pattern, $path)) {
                // Если это действие в рамках той же локации, не считаем это сменой локации
                \Log::info("Обнаружен URL действия, не сбрасываем цель", [
                    'path' => $path,
                    'pattern' => $pattern
                ]);
                return null;
            }
        }

        // Проверяем, является ли текущий путь частью боевого пути
        $battlePaths = [
            'battle/outposts/dawn_fort',
            'battle/outposts/sandy_stronghold',
            'battle/outposts/elven_haven',
            'battle/mines/tarnmore_quarry'
        ];

        // Проверка для кастомных шахт
        if (preg_match('#^battle/mines/([^/]+)#', $path, $matches)) {
            $slug = $matches[1];

            // Проверяем, есть ли локация с таким slug в базе данных
            try {
                $mineLocation = \App\Models\MineLocation::where('slug', $slug)
                    ->where('is_active', true)
                    ->first();

                if ($mineLocation) {
                    \Log::info("[Resolve Location] Определена кастомная шахта по slug '{$slug}': '{$mineLocation->name}'", [
                        'path' => $path,
                        'location_id' => $mineLocation->id,
                        'location_name' => $mineLocation->name
                    ]);
                    return $mineLocation->name;
                }
            } catch (\Exception $e) {
                \Log::error("[Resolve Location] Ошибка при определении кастомной шахты: " . $e->getMessage(), [
                    'path' => $path,
                    'slug' => $slug
                ]);
            }
        }

        // Извлекаем базовый путь (без параметров)
        $basePath = explode('/', $path);
        if (count($basePath) >= 3) {
            $basePathStr = $basePath[0] . '/' . $basePath[1] . '/' . $basePath[2];

            foreach ($battlePaths as $battlePath) {
                if ($basePathStr === $battlePath) {
                    // Если базовый путь совпадает с боевым путем, возвращаем соответствующую локацию
                    // вместо определения новой локации
                    if ($battlePath === 'battle/outposts/dawn_fort') {
                        \Log::info("[Resolve Location Depth:0] Путь '{$path}' определен как путь действия (содержит '/attack'). ", [
                            'base_path' => $basePathStr,
                            'battle_path' => $battlePath
                        ]);
                        return 'Форт Рассвета';
                    } else if ($battlePath === 'battle/outposts/sandy_stronghold') {
                        return 'Песчаный Оплот';
                    } else if ($battlePath === 'battle/outposts/elven_haven') {
                        return 'Эльфийская Гавань';
                    } else if ($battlePath === 'battle/mines/tarnmore_quarry') {
                        return 'Тарнмор';
                    }
                }
            }
        }

        // Маппинг URL-путей к названиям локаций
        $locationMappings = [
            'battle/outposts/sandy_stronghold' => 'Песчаный Оплот',
            'battle/outposts/elven_haven' => 'Эльфийская Гавань',
            'battle/mines/tarnmore_quarry' => 'Тарнмор',
            'battle/outposts/dawn_fort' => 'Форт Рассвета',
            'battle/outposts' => 'Аванпосты'
        ];

        // Проверяем, является ли путь путем к пользовательскому аванпосту
        if (preg_match('#^battle/outposts/custom/(\d+)#', $path, $matches)) {
            $outpostId = $matches[1];
            // Получаем название локации из базы данных
            try {
                $outpostLocation = \App\Models\OutpostLocation::where('id', $outpostId)
                    ->where('is_active', true)
                    ->first();

                if ($outpostLocation) {
                    \Log::info("Определена локация пользовательского аванпоста из URL", [
                        'path' => $path,
                        'outpost_id' => $outpostId,
                        'location_name' => $outpostLocation->name
                    ]);
                    return $outpostLocation->name;
                }
            } catch (\Exception $e) {
                \Log::error("Ошибка при определении локации пользовательского аванпоста", [
                    'path' => $path,
                    'outpost_id' => $outpostId,
                    'error' => $e->getMessage()
                ]);
            }
        }

        foreach ($locationMappings as $urlPath => $locationName) {
            if (strpos($path, $urlPath) === 0) {
                return $locationName;
            }
        }

        return null;
    }

    /**
     * Определяет, является ли запрос сменой локации
     */
    private function isLocationChangeRequest(Request $request): bool
    {
        $path = $request->path();

        // Паттерны URL для смены локаций
        $locationChangePatterns = [
            '/^battle\/outposts\/\d+$/',
            '/^battle\/mines\/[^\/]+$/',
            '/^dungeons\/\d+$/',
            '/^dungeons\/\d+\/lobby$/',
            '/^dungeons\/\d+\/battle$/',
            '/^home$/',
            '/^dashboard$/',
            '/^square$/',
            '/^shop$/',
            '/^masters$/',
            '/^farming$/',
            '/^professions$/',
            '/^online$/',
            '/^forum$/',
            '/^market$/',
            '/^bank$/',
        ];

        foreach ($locationChangePatterns as $pattern) {
            if (preg_match($pattern, $path)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Получает сообщение об оглушении
     */
    private function getStunMessage($user): string
    {
        // Получаем активный эффект оглушения
        $stunEffect = $user->activeEffects()
            ->get()
            ->first(function ($effect) {
                return $effect->isActive() && $effect->isStunEffect();
            });

        if ($stunEffect && isset($stunEffect->effect_data['message'])) {
            return $stunEffect->effect_data['message'];
        }

        return '⚡ Вы оглушены и не можете перемещаться!';
    }
}
