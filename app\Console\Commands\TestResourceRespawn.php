<?php

namespace App\Console\Commands;

use App\Models\SpawnedResource;
use App\Models\Resource;
use App\Models\Location;
use App\Services\LocationResourceService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class TestResourceRespawn extends Command
{
    protected $signature = 'test:resource-respawn {--cleanup : Удалить тестовые ресурсы}';
    protected $description = 'Тестирование системы респавна ресурсов';

    public function handle()
    {
        if ($this->option('cleanup')) {
            return $this->cleanup();
        }

        $this->info('🧪 Тестирование системы респавна ресурсов...');
        $this->newLine();

        // Шаг 1: Создаем тестовый ресурс
        $this->info('1️⃣ Создание тестового ресурса...');
        $testResource = $this->createTestResource();

        if (!$testResource) {
            $this->error('❌ Не удалось создать тестовый ресурс');
            return Command::FAILURE;
        }

        $this->line("   ✅ Создан ресурс ID: {$testResource->id}");
        $this->line("   📊 Время респавна: {$testResource->respawn_time} минут");
        $this->line("   🎯 Шанс респавна: {$testResource->spawn_chance}%");
        $this->newLine();

        // Шаг 2: Симулируем истощение ресурса
        $this->info('2️⃣ Симуляция истощения ресурса...');
        $service = app(LocationResourceService::class);

        // Устанавливаем прочность в 1, чтобы следующий удар истощил ресурс
        $testResource->durability = 1;
        $testResource->save();

        // Симулируем добычу (истощение)
        $result = $service->decreaseDurability($testResource, 1);

        if ($result) {
            $testResource->refresh();
            $this->line("   ✅ Ресурс истощен");
            $this->line("   📊 Активен: " . ($testResource->is_active ? 'Да' : 'Нет'));
            $this->line("   ⏰ Время респавна установлено: " . $testResource->respawn_at->format('Y-m-d H:i:s'));
            $this->line("   🕐 Текущее время: " . now()->format('Y-m-d H:i:s'));
        } else {
            $this->error('   ❌ Не удалось истощить ресурс');
            return Command::FAILURE;
        }
        $this->newLine();

        // Шаг 3: Проверяем логику респавна до времени
        $this->info('3️⃣ Проверка логики респавна (до времени)...');
        $resourcesBeforeTime = SpawnedResource::where('is_active', false)
            ->where('respawn_at', '<=', Carbon::now())
            ->where('id', $testResource->id)
            ->count();

        $this->line("   📊 Ресурсов готовых к респауну: {$resourcesBeforeTime}");

        if ($resourcesBeforeTime > 0) {
            $this->warn('   ⚠️ Ресурс готов к респауну раньше времени!');
        } else {
            $this->line("   ✅ Ресурс корректно ожидает времени респавна");
        }
        $this->newLine();

        // Шаг 4: Принудительно устанавливаем время респавна в прошлое
        $this->info('4️⃣ Принудительная установка времени респавна в прошлое...');
        $testResource->respawn_at = Carbon::now()->subMinutes(1);
        $testResource->save();

        $this->line("   ✅ Время респавна установлено: " . $testResource->respawn_at->format('Y-m-d H:i:s'));
        $this->line("   🕐 Текущее время: " . now()->format('Y-m-d H:i:s'));
        $this->newLine();

        // Шаг 5: Проверяем логику респавна после времени
        $this->info('5️⃣ Проверка логики респавна (после времени)...');
        $resourcesAfterTime = SpawnedResource::where('is_active', false)
            ->where('respawn_at', '<=', Carbon::now())
            ->where('id', $testResource->id)
            ->count();

        $this->line("   📊 Ресурсов готовых к респауну: {$resourcesAfterTime}");
        $this->newLine();

        // Шаг 6: Запускаем респаун
        $this->info('6️⃣ Запуск процесса респавна...');
        $service->respawnResources();

        $testResource->refresh();
        $this->line("   📊 Активен после респавна: " . ($testResource->is_active ? 'Да' : 'Нет'));
        $this->line("   💪 Прочность: {$testResource->durability}/{$testResource->max_durability}");
        $this->line("   ⏰ Время респавна: " . ($testResource->respawn_at ?? 'null'));
        $this->newLine();

        // Шаг 7: Результаты
        $this->info('7️⃣ Результаты тестирования:');
        if ($testResource->is_active && $testResource->durability > 0) {
            $this->line("   ✅ Тест ПРОЙДЕН: Ресурс успешно респавнился");
        } else {
            $this->error("   ❌ Тест ПРОВАЛЕН: Ресурс не респавнился");
            $this->line("   🔍 Проверьте логи для деталей");
        }

        $this->newLine();
        $this->info('💡 Для очистки тестовых данных запустите: php artisan test:resource-respawn --cleanup');

        return Command::SUCCESS;
    }

    private function createTestResource(): ?SpawnedResource
    {
        // Получаем первый доступный ресурс и локацию
        $resource = Resource::first();
        $location = Location::first();

        if (!$resource || !$location) {
            return null;
        }

        return SpawnedResource::create([
            'resource_id' => $resource->id,
            'location_id' => $location->id,
            'is_active' => true,
            'durability' => 100,
            'max_durability' => 100,
            'respawn_time' => 2, // 2 минуты для быстрого тестирования
            'spawn_chance' => 100, // 100% шанс для гарантированного респавна
            'respawn_at' => null
        ]);
    }

    private function cleanup(): int
    {
        $this->info('🧹 Очистка тестовых ресурсов...');

        // Удаляем тестовые ресурсы (с коротким временем респавна)
        $deleted = SpawnedResource::where('respawn_time', 2)->delete();

        $this->line("   ✅ Удалено тестовых ресурсов: {$deleted}");

        return Command::SUCCESS;
    }
}
