<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearRedirectCounters extends Command
{
    protected $signature = 'clear:redirect-counters {user_id?}';
    protected $description = 'Очистка счетчиков переадресаций для предотвращения циклов';

    public function handle()
    {
        $userId = $this->argument('user_id');
        
        if ($userId) {
            $this->clearUserCounters($userId);
            $this->info("✅ Счетчики переадресаций для пользователя {$userId} очищены");
        } else {
            $this->clearAllCounters();
            $this->info("✅ Все счетчики переадресаций очищены");
        }
    }

    private function clearUserCounters(int $userId): void
    {
        Cache::forget("stun_redirect_count:{$userId}");
        Cache::forget("forced_redirect_count:{$userId}");
        Cache::forget("dungeon_completion_redirect:{$userId}");
        Cache::forget("dungeon_forced_redirect:{$userId}");
    }

    private function clearAllCounters(): void
    {
        // Очищаем все ключи, связанные с переадресациями
        $patterns = [
            'stun_redirect_count:*',
            'forced_redirect_count:*',
            'dungeon_completion_redirect:*',
            'dungeon_forced_redirect:*'
        ];

        // Поскольку Laravel Cache не поддерживает wildcard удаление напрямую,
        // мы можем использовать Redis команды если используется Redis
        try {
            if (config('cache.default') === 'redis') {
                $redis = Cache::getRedis();
                foreach ($patterns as $pattern) {
                    $keys = $redis->keys($pattern);
                    if (!empty($keys)) {
                        $redis->del($keys);
                        $this->line("Удалено ключей по паттерну {$pattern}: " . count($keys));
                    }
                }
            } else {
                $this->warn('Для полной очистки рекомендуется использовать Redis как драйвер кэша');
                $this->line('Выполняется частичная очистка...');
                
                // Частичная очистка для первых 100 пользователей
                for ($i = 1; $i <= 100; $i++) {
                    $this->clearUserCounters($i);
                }
            }
        } catch (\Exception $e) {
            $this->error('Ошибка при очистке: ' . $e->getMessage());
        }
    }
}
