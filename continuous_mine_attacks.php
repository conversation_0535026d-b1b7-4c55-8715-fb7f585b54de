<?php

require_once 'vendor/autoload.php';

use App\Jobs\MineAutoAttackJob;
use App\Services\MineDetectionService;
use App\Services\MineTargetDistributionService;
use App\Services\BattleLogService;
use App\Services\PlayerHealthService;
use App\Services\CombatFormulaService;
use App\Services\LogFormattingService;

// Загружаем Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "⚔️ НЕПРЕРЫВНЫЕ АТАКИ МОБОВ В РУДНИКАХ\n";
echo "====================================\n\n";

echo "🚀 Запуск системы непрерывных атак...\n";
echo "⏰ Интервал: 10 секунд\n";
echo "🛑 Для остановки нажмите Ctrl+C\n\n";

$attackCount = 0;
$startTime = time();

while (true) {
    try {
        $attackCount++;
        $currentTime = date('H:i:s');
        
        echo "[{$currentTime}] Атака #{$attackCount} - ";
        
        $job = new MineAutoAttackJob();
        
        $job->handle(
            app(MineDetectionService::class),
            app(MineTargetDistributionService::class),
            app(BattleLogService::class),
            app(PlayerHealthService::class),
            app(CombatFormulaService::class),
            app(LogFormattingService::class)
        );
        
        echo "✅ Выполнена\n";
        
        // Каждые 10 атак показываем статистику
        if ($attackCount % 10 === 0) {
            $elapsed = time() - $startTime;
            $avgInterval = $elapsed / $attackCount;
            echo "\n📊 СТАТИСТИКА:\n";
            echo "- Всего атак: {$attackCount}\n";
            echo "- Время работы: {$elapsed} секунд\n";
            echo "- Средний интервал: " . round($avgInterval, 1) . " секунд\n\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Ошибка: {$e->getMessage()}\n";
    }
    
    // Ждем 10 секунд
    sleep(10);
}
