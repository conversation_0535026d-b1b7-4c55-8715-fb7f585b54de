<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\User;

class DiagnoseRedirectLoop extends Command
{
    protected $signature = 'diagnose:redirect-loop {user_id?}';
    protected $description = 'Диагностика проблем с бесконечными переадресациями';

    public function handle()
    {
        $this->info('🔍 ДИАГНОСТИКА ПРОБЛЕМ С ПЕРЕАДРЕСАЦИЯМИ');
        $this->newLine();

        $userId = $this->argument('user_id');
        
        // 1. Проверяем middleware конфигурацию
        $this->checkMiddlewareConfiguration();
        
        // 2. Проверяем потенциальные циклы в маршрутах
        $this->checkRouteRedirectCycles();
        
        // 3. Проверяем состояние конкретного пользователя
        if ($userId) {
            $this->checkUserState($userId);
        }
        
        // 4. Проверяем кэш на наличие принудительных редиректов
        $this->checkForcedRedirects();
        
        // 5. Анализируем логи
        $this->analyzeLogs();
        
        $this->newLine();
        $this->info('✅ Диагностика завершена');
    }

    private function checkMiddlewareConfiguration()
    {
        $this->info('1. 🔧 Проверка конфигурации middleware:');
        
        // Проверяем web группу middleware
        $webMiddleware = app('router')->getMiddlewareGroups()['web'] ?? [];
        $this->line('   Web группа middleware:');
        foreach ($webMiddleware as $middleware) {
            $this->line("     - " . class_basename($middleware));
        }
        
        // Проверяем алиасы middleware
        $middlewareAliases = app('router')->getMiddleware();
        $stunRelatedAliases = array_filter($middlewareAliases, function($class, $alias) {
            return str_contains($alias, 'stun') || str_contains($alias, 'redirect') || str_contains($alias, 'forced');
        }, ARRAY_FILTER_USE_BOTH);
        
        $this->line('   Middleware связанные с переадресациями:');
        foreach ($stunRelatedAliases as $alias => $class) {
            $this->line("     - {$alias}: " . class_basename($class));
        }
    }

    private function checkRouteRedirectCycles()
    {
        $this->info('2. 🔄 Проверка потенциальных циклов в маршрутах:');
        
        $routes = Route::getRoutes();
        $problematicRoutes = [];
        
        foreach ($routes as $route) {
            $middleware = $route->middleware();
            $routeName = $route->getName();
            
            // Ищем маршруты с множественными middleware для стана
            $stunMiddleware = array_filter($middleware, function($m) {
                return str_contains($m, 'stun') || str_contains($m, 'check.stun');
            });
            
            if (count($stunMiddleware) > 1) {
                $problematicRoutes[] = [
                    'route' => $routeName ?: $route->uri(),
                    'middleware' => $stunMiddleware,
                    'issue' => 'Множественные stun middleware'
                ];
            }
            
            // Ищем маршруты с потенциально конфликтующими middleware
            $redirectMiddleware = array_filter($middleware, function($m) {
                return str_contains($m, 'redirect') || str_contains($m, 'forced');
            });
            
            if (count($redirectMiddleware) > 0 && in_array('check.stun.actions', $middleware)) {
                $problematicRoutes[] = [
                    'route' => $routeName ?: $route->uri(),
                    'middleware' => array_merge($stunMiddleware, $redirectMiddleware),
                    'issue' => 'Конфликт stun и redirect middleware'
                ];
            }
        }
        
        if (empty($problematicRoutes)) {
            $this->line('   ✅ Проблемных маршрутов не найдено');
        } else {
            $this->line('   ⚠️ Найдены потенциально проблемные маршруты:');
            foreach ($problematicRoutes as $route) {
                $this->line("     - {$route['route']}: {$route['issue']}");
                $this->line("       Middleware: " . implode(', ', $route['middleware']));
            }
        }
    }

    private function checkUserState($userId)
    {
        $this->info("3. 👤 Проверка состояния пользователя ID: {$userId}:");
        
        $user = User::find($userId);
        if (!$user) {
            $this->error("   ❌ Пользователь с ID {$userId} не найден");
            return;
        }
        
        // Проверяем состояние стана
        $isStunned = $user->isStunned();
        $this->line("   Статус стана: " . ($isStunned ? '⚡ ОГЛУШЕН' : '✅ НЕ ОГЛУШЕН'));
        
        if ($isStunned) {
            $activeEffects = $user->activeEffects()->get();
            $stunEffects = $activeEffects->filter(function($effect) {
                return $effect->isActive() && $effect->isStunEffect();
            });
            
            $this->line("   Активные эффекты стана:");
            foreach ($stunEffects as $effect) {
                $this->line("     - {$effect->effect_type}: до " . $effect->expires_at);
            }
        }
        
        // Проверяем статус в подземелье
        if ($user->in_dungeon_id) {
            $this->line("   В подземелье: ID {$user->in_dungeon_id}, статус: {$user->in_dungeon_status}");
        }
        
        // Проверяем текущую локацию
        $currentLocation = $user->statistics->current_location ?? 'Неизвестно';
        $this->line("   Текущая локация: {$currentLocation}");
    }

    private function checkForcedRedirects()
    {
        $this->info('4. 🎯 Проверка принудительных редиректов в кэше:');
        
        $foundRedirects = false;
        
        // Проверим несколько пользователей
        $users = User::limit(10)->get();
        foreach ($users as $user) {
            $forcedRedirect = Cache::get("dungeon_forced_redirect:{$user->id}");
            $completionRedirect = Cache::get("dungeon_completion_redirect:{$user->id}");
            
            if ($forcedRedirect) {
                $this->line("   ⚠️ Принудительный редирект для пользователя {$user->id}:");
                $this->line("     " . json_encode($forcedRedirect));
                $foundRedirects = true;
            }
            
            if ($completionRedirect) {
                $this->line("   ⚠️ Редирект завершения для пользователя {$user->id}:");
                $this->line("     " . json_encode($completionRedirect));
                $foundRedirects = true;
            }
        }
        
        if (!$foundRedirects) {
            $this->line('   ✅ Активных принудительных редиректов не найдено');
        }
    }

    private function analyzeLogs()
    {
        $this->info('5. 📋 Анализ логов:');
        
        $logFile = storage_path('logs/laravel.log');
        if (!file_exists($logFile)) {
            $this->line('   ⚠️ Основной лог файл не найден');
            return;
        }
        
        // Читаем последние 100 строк лога
        $lines = array_slice(file($logFile), -100);
        $redirectLines = array_filter($lines, function($line) {
            return str_contains($line, 'redirect') || 
                   str_contains($line, 'CheckForcedDungeonRedirect') ||
                   str_contains($line, 'CheckDungeonCompletionRedirect') ||
                   str_contains($line, 'CheckPlayerStun');
        });
        
        if (empty($redirectLines)) {
            $this->line('   ✅ Записей о переадресациях в логах не найдено');
        } else {
            $this->line('   ⚠️ Найдены записи о переадресациях:');
            foreach (array_slice($redirectLines, -5) as $line) {
                $this->line('     ' . trim($line));
            }
        }
    }
}
