/**
 * Система управления эффектами оглушения
 * Блокирует интерфейс при активных эффектах оглушения
 */

class StunManager {
    constructor() {
        this.isStunned = false;
        this.stunCheckInterval = null;
        this.blockedElements = new Set();
        this.originalHandlers = new Map();

        this.init();
    }

    /**
     * Инициализация системы
     */
    init() {
        this.checkStunStatus();
        this.startPeriodicCheck();
        this.setupEventListeners();

        console.log("StunManager инициализирован");
    }

    /**
     * Запуск периодической проверки состояния оглушения
     */
    startPeriodicCheck() {
        // Проверяем каждые 2 секунды
        this.stunCheckInterval = setInterval(() => {
            this.checkStunStatus();
        }, 2000);
    }

    /**
     * Остановка периодической проверки
     */
    stopPeriodicCheck() {
        if (this.stunCheckInterval) {
            clearInterval(this.stunCheckInterval);
            this.stunCheckInterval = null;
        }
    }

    /**
     * Проверка текущего состояния оглушения
     */
    async checkStunStatus() {
        try {
            const response = await fetch("/api/user/effects/stun-status", {
                method: "GET",
                headers: {
                    "X-CSRF-TOKEN": document
                        .querySelector('meta[name="csrf-token"]')
                        .getAttribute("content"),
                    Accept: "application/json",
                },
            });

            if (response.ok) {
                const data = await response.json();
                this.updateStunStatus(data.is_stunned, data.stun_message);
            }
        } catch (error) {
            console.error("Ошибка при проверке состояния оглушения:", error);
        }
    }

    /**
     * Обновление состояния оглушения
     */
    updateStunStatus(isStunned, stunMessage = null) {
        if (this.isStunned !== isStunned) {
            this.isStunned = isStunned;

            if (isStunned) {
                this.blockInterface(stunMessage);
            } else {
                this.unblockInterface();
            }
        }
    }

    /**
     * Блокировка интерфейса
     */
    blockInterface(message = "⚡ Вы оглушены и не можете действовать!") {
        console.log("Блокировка интерфейса из-за оглушения");

        // Блокируем формы
        this.blockForms();

        // Блокируем кнопки
        this.blockButtons();

        // Блокируем ссылки навигации
        this.blockNavigationLinks();

        // Показываем сообщение об оглушении
        this.showStunMessage(message);

        // Добавляем визуальные эффекты
        this.addStunVisualEffects();
    }

    /**
     * Разблокировка интерфейса
     */
    unblockInterface() {
        console.log("Разблокировка интерфейса");

        // Разблокируем все заблокированные элементы
        this.unblockAllElements();

        // Скрываем сообщение об оглушении
        this.hideStunMessage();

        // Убираем визуальные эффекты
        this.removeStunVisualEffects();
    }

    /**
     * Блокировка форм
     */
    blockForms() {
        const forms = document.querySelectorAll("form");
        forms.forEach((form) => {
            if (!form.classList.contains("stun-blocked")) {
                this.blockElement(form);

                // Блокируем отправку формы
                const submitHandler = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showStunNotification();
                    return false;
                };

                form.addEventListener("submit", submitHandler);
                this.originalHandlers.set(form, {
                    type: "submit",
                    handler: submitHandler,
                });
            }
        });
    }

    /**
     * Блокировка кнопок
     */
    blockButtons() {
        const buttons = document.querySelectorAll(
            'button, input[type="submit"], input[type="button"]'
        );
        buttons.forEach((button) => {
            if (!button.classList.contains("stun-blocked")) {
                this.blockElement(button);

                const clickHandler = (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showStunNotification();
                    return false;
                };

                button.addEventListener("click", clickHandler);
                this.originalHandlers.set(button, {
                    type: "click",
                    handler: clickHandler,
                });
            }
        });
    }

    /**
     * Блокировка навигационных ссылок
     */
    blockNavigationLinks() {
        // Блокируем основные навигационные ссылки
        const navSelectors = [
            'a[href]:not([href^="#"]):not([href^="javascript:"])', // Все ссылки кроме якорей и JS
            ".navigation-button-link", // Основная навигация
            'a[href*="/battle/"]', // Боевые локации
            'a[href*="/dungeons/"]', // Подземелья
            'a[href*="/shop"]', // Магазин
            'a[href*="/masters"]', // Таверна
            'a[href*="/farming"]', // Фермерство
            'a[href*="/professions"]', // Профессии
            'a[href*="/square"]', // Площадь
            'a[href*="/party"]', // Группы
            'a[href*="/guilds"]', // Гильдии
        ];

        navSelectors.forEach((selector) => {
            const links = document.querySelectorAll(selector);
            links.forEach((link) => {
                // Исключаем некоторые ссылки, которые должны оставаться доступными
                const href = link.getAttribute("href") || "";
                const isAllowed = this.isLinkAllowed(href);

                if (!isAllowed && !link.classList.contains("stun-blocked")) {
                    this.blockElement(link);

                    const clickHandler = (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        this.showStunNotification();
                        return false;
                    };

                    link.addEventListener("click", clickHandler, true); // Используем capture phase
                    this.originalHandlers.set(link, {
                        type: "click",
                        handler: clickHandler,
                    });
                }
            });
        });
    }

    /**
     * Проверяет, разрешена ли ссылка при стане
     */
    isLinkAllowed(href) {
        const allowedPatterns = [
            "/inventory", // Рюкзак
            "/character", // Персонаж
            "/user/profile", // Профиль
            "/messages", // Сообщения
            "/settings", // Настройки
            "/auth/logout", // Выход
            "/battle/defeat", // Страница поражения
            "/battle/respawn", // Возрождение
        ];

        return allowedPatterns.some((pattern) => href.includes(pattern));
    }

    /**
     * Блокировка отдельного элемента
     */
    blockElement(element) {
        element.classList.add("stun-blocked");
        element.style.pointerEvents = "none";
        element.style.opacity = "0.4";
        element.style.cursor = "not-allowed";
        element.style.filter = "grayscale(1) brightness(0.7)";
        element.style.transition = "all 0.3s ease";

        // Добавляем визуальный индикатор блокировки
        if (!element.querySelector(".stun-overlay")) {
            const overlay = document.createElement("div");
            overlay.className = "stun-overlay";
            overlay.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(255, 255, 0, 0.1);
                border: 1px solid rgba(255, 255, 0, 0.3);
                pointer-events: none;
                z-index: 1000;
                animation: stunPulse 2s infinite;
            `;

            // Делаем элемент относительно позиционированным для overlay
            if (getComputedStyle(element).position === "static") {
                element.style.position = "relative";
            }

            element.appendChild(overlay);
        }

        this.blockedElements.add(element);
    }

    /**
     * Разблокировка всех элементов
     */
    unblockAllElements() {
        this.blockedElements.forEach((element) => {
            element.classList.remove("stun-blocked");
            element.style.pointerEvents = "";
            element.style.opacity = "";
            element.style.cursor = "";
            element.style.filter = "";
            element.style.transition = "";

            // Удаляем overlay
            const overlay = element.querySelector(".stun-overlay");
            if (overlay) {
                overlay.remove();
            }

            // Восстанавливаем позиционирование, если мы его меняли
            if (
                element.style.position === "relative" &&
                !element.dataset.originalPosition
            ) {
                element.style.position = "";
            }
        });

        // Удаляем обработчики событий
        this.originalHandlers.forEach((handlerInfo, element) => {
            element.removeEventListener(
                handlerInfo.type,
                handlerInfo.handler,
                true
            );
        });

        this.blockedElements.clear();
        this.originalHandlers.clear();
    }

    /**
     * Показать сообщение об оглушении
     */
    showStunMessage(message) {
        // Удаляем старое сообщение, если есть
        this.hideStunMessage();

        const stunMessage = document.createElement("div");
        stunMessage.id = "stun-message";
        stunMessage.className =
            "fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-gradient-to-r from-yellow-600 to-orange-600 text-white px-6 py-3 rounded-lg shadow-lg animate-pulse border-2 border-yellow-400";
        stunMessage.innerHTML = `
            <div class="flex items-center space-x-2">
                <span class="text-xl">⚡</span>
                <span class="font-medium">${message}</span>
            </div>
        `;

        document.body.appendChild(stunMessage);
    }

    /**
     * Скрыть сообщение об оглушении
     */
    hideStunMessage() {
        const stunMessage = document.getElementById("stun-message");
        if (stunMessage) {
            stunMessage.remove();
        }
    }

    /**
     * Показать уведомление при попытке действия
     */
    showStunNotification() {
        // Создаем временное уведомление
        const notification = document.createElement("div");
        notification.className =
            "fixed top-16 left-1/2 transform -translate-x-1/2 z-50 bg-red-600 text-white px-4 py-2 rounded shadow-lg";
        notification.textContent = "Действие заблокировано - вы оглушены!";

        document.body.appendChild(notification);

        // Удаляем через 2 секунды
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 2000);
    }

    /**
     * Добавить визуальные эффекты оглушения
     */
    addStunVisualEffects() {
        document.body.classList.add("stunned");

        // Добавляем CSS стили, если их еще нет
        if (!document.getElementById("stun-styles")) {
            const style = document.createElement("style");
            style.id = "stun-styles";
            style.textContent = `
                .stunned {
                    filter: saturate(0.7) brightness(0.9);
                }
                .stun-blocked {
                    transition: all 0.3s ease;
                }
                @keyframes stunPulse {
                    0% {
                        background-color: rgba(255, 255, 0, 0.1);
                        border-color: rgba(255, 255, 0, 0.3);
                    }
                    50% {
                        background-color: rgba(255, 255, 0, 0.2);
                        border-color: rgba(255, 255, 0, 0.5);
                    }
                    100% {
                        background-color: rgba(255, 255, 0, 0.1);
                        border-color: rgba(255, 255, 0, 0.3);
                    }
                }
                .stun-overlay {
                    animation: stunPulse 2s infinite;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Убрать визуальные эффекты оглушения
     */
    removeStunVisualEffects() {
        document.body.classList.remove("stunned");
    }

    /**
     * Настройка обработчиков событий
     */
    setupEventListeners() {
        // Обработчик для обновления страницы
        window.addEventListener("beforeunload", () => {
            this.stopPeriodicCheck();
        });

        // Обработчик для фокуса/потери фокуса окна
        document.addEventListener("visibilitychange", () => {
            if (!document.hidden) {
                this.checkStunStatus();
            }
        });
    }

    /**
     * Принудительная проверка состояния
     */
    forceCheck() {
        this.checkStunStatus();
    }

    /**
     * Получить текущее состояние
     */
    getStunStatus() {
        return this.isStunned;
    }

    /**
     * Уничтожение экземпляра
     */
    destroy() {
        this.stopPeriodicCheck();
        this.unblockInterface();
        this.removeStunVisualEffects();

        const stunStyles = document.getElementById("stun-styles");
        if (stunStyles) {
            stunStyles.remove();
        }
    }
}

// Создаем глобальный экземпляр
window.StunManager = new StunManager();

// Экспорт для модульной системы
export default StunManager;
