<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Listeners\ForceDungeonRedirect;

/**
 * Middleware для проверки принудительного редиректа в подземелье
 */
class CheckForcedDungeonRedirect
{
    /**
     * Обработка запроса
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check()) {
            $user = Auth::user();
            $currentPath = $request->path();

            // ВАЖНО: Проверяем состояние стана ПЕРЕД принудительным редиректом
            // Если игрок оглушен, не выполняем принудительный редирект
            if ($user->isStunned()) {
                Log::info('[CheckForcedDungeonRedirect] Пропуск принудительного редиректа - игрок оглушен', [
                    'user_id' => $user->id,
                    'current_path' => $currentPath
                ]);
                return $next($request);
            }

            // Получаем данные принудительного редиректа
            $redirectData = ForceDungeonRedirect::getForcedRedirectForUser($user->id);

            if ($redirectData) {
                $dungeonId = $redirectData['dungeon_id'];
                $redirectUrl = $redirectData['redirect_url'];

                // Проверяем, что пользователь не находится уже на целевой странице
                $targetBattlePath = "dungeons/{$dungeonId}/battle";

                if (!str_contains($currentPath, $targetBattlePath)) {
                    // Проверяем, не находимся ли мы в цикле переадресаций
                    $redirectCount = $this->getRedirectCount($user->id);
                    if ($redirectCount >= 3) {
                        Log::error('[CheckForcedDungeonRedirect] Обнаружен цикл переадресаций, прерываем', [
                            'user_id' => $user->id,
                            'redirect_count' => $redirectCount,
                            'current_path' => $currentPath
                        ]);
                        ForceDungeonRedirect::clearForcedRedirectForUser($user->id);
                        $this->clearRedirectCount($user->id);
                        return $next($request);
                    }

                    // Увеличиваем счетчик переадресаций
                    $this->incrementRedirectCount($user->id);

                    // Обновляем статус пользователя в подземелье
                    $user->update([
                        'in_dungeon_id' => $dungeonId,
                        'in_dungeon_status' => 'battle',
                        'dungeon_entered_at' => now()
                    ]);

                    // Очищаем принудительный редирект
                    ForceDungeonRedirect::clearForcedRedirectForUser($user->id);

                    Log::info('[CheckForcedDungeonRedirect] Принудительный редирект выполнен', [
                        'user_id' => $user->id,
                        'from_path' => $currentPath,
                        'to_url' => $redirectUrl,
                        'dungeon_id' => $dungeonId,
                        'redirect_count' => $redirectCount + 1
                    ]);

                    return redirect($redirectUrl)->with('info', 'Ваша группа начала подземелье!');
                } else {
                    // Пользователь уже на целевой странице, очищаем редирект и счетчик
                    ForceDungeonRedirect::clearForcedRedirectForUser($user->id);
                    $this->clearRedirectCount($user->id);
                }
            }
        }

        return $next($request);
    }

    /**
     * Получает количество переадресаций для пользователя
     */
    private function getRedirectCount(int $userId): int
    {
        return (int) cache()->get("forced_redirect_count:{$userId}", 0);
    }

    /**
     * Увеличивает счетчик переадресаций
     */
    private function incrementRedirectCount(int $userId): void
    {
        $count = $this->getRedirectCount($userId) + 1;
        cache()->put("forced_redirect_count:{$userId}", $count, 300); // 5 минут
    }

    /**
     * Очищает счетчик переадресаций
     */
    private function clearRedirectCount(int $userId): void
    {
        cache()->forget("forced_redirect_count:{$userId}");
    }
}
