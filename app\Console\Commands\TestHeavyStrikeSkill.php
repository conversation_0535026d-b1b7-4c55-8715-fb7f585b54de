<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Mob;
use App\Models\User;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Models\ActiveEffect;
use App\Services\SkillService;

class TestHeavyStrikeSkill extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:heavy-strike {--clear : Очистить эффекты стана} {--setup : Настроить тестовые данные}';

    /**
     * The console command description.
     */
    protected $description = 'Тестирование скилла "Тяжелый удар" с кастомной длительностью';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('clear')) {
            $this->clearStunEffects();
            return;
        }

        if ($this->option('setup')) {
            $this->setupTestData();
            return;
        }

        $this->runTest();
    }

    /**
     * Очистка эффектов стана
     */
    protected function clearStunEffects()
    {
        $this->info('🧹 Очистка эффектов стана...');
        
        $deleted = ActiveEffect::where('effect_data->type', 'stun')->delete();
        
        $this->info("✅ Удалено эффектов стана: {$deleted}");
    }

    /**
     * Настройка тестовых данных
     */
    protected function setupTestData()
    {
        $this->info('⚙️ Настройка тестовых данных...');

        // Находим тестового моба
        $testMob = Mob::where('name', 'имимим')->first();
        if (!$testMob) {
            $this->error('❌ Тестовый моб "имимим" не найден!');
            return;
        }

        // Находим шаблон скилла
        $heavyStrike = MobSkillTemplate::where('name', 'Тяжелый удар')->first();
        if (!$heavyStrike) {
            $this->error('❌ Шаблон скилла "Тяжелый удар" не найден!');
            return;
        }

        // Настраиваем скилл моба
        $mobSkill = MobSkill::where('mob_id', $testMob->id)
            ->where('skill_template_id', $heavyStrike->id)
            ->first();

        if (!$mobSkill) {
            $mobSkill = MobSkill::create([
                'mob_id' => $testMob->id,
                'skill_template_id' => $heavyStrike->id,
                'chance' => 100,
                'custom_duration' => 10
            ]);
            $this->info('✅ Создана новая привязка скилла');
        } else {
            $mobSkill->update([
                'chance' => 100,
                'custom_duration' => 10
            ]);
            $this->info('✅ Обновлена существующая привязка скилла');
        }

        $this->info("Моб: {$testMob->name} (ID: {$testMob->id})");
        $this->info("Локация: {$testMob->location}");
        $this->info("Скилл: {$heavyStrike->name}");
        $this->info("Шанс: {$mobSkill->chance}%");
        $this->info("Кастомная длительность: {$mobSkill->custom_duration} секунд");
        $this->info("Эффективная длительность: {$mobSkill->getEffectiveDuration()} секунд");
        $this->info("Шаблон длительность: {$heavyStrike->duration} секунд");
    }

    /**
     * Запуск теста
     */
    protected function runTest()
    {
        $this->info('🧪 Тестирование скилла "Тяжелый удар"...');

        // Находим тестовые данные
        $testMob = Mob::where('name', 'имимим')->first();
        $testUser = User::where('name', 'admin')->first();

        if (!$testMob || !$testUser) {
            $this->error('❌ Тестовые данные не найдены!');
            return;
        }

        $this->info("Тестовый моб: {$testMob->name} (ID: {$testMob->id})");
        $this->info("Тестовый пользователь: {$testUser->name} (ID: {$testUser->id})");

        // Проверяем скилл моба
        $mobSkill = MobSkill::where('mob_id', $testMob->id)->first();
        if (!$mobSkill) {
            $this->error('❌ У моба нет скиллов!');
            $this->info('💡 Выполните: php artisan test:heavy-strike --setup');
            return;
        }

        $this->info("Скилл моба: {$mobSkill->skillTemplate->name}");
        $this->info("Эффективная длительность: {$mobSkill->getEffectiveDuration()} секунд");

        // Очищаем старые эффекты
        ActiveEffect::where('target_type', 'player')
            ->where('target_id', $testUser->id)
            ->where('effect_data->type', 'stun')
            ->delete();

        // Применяем скилл
        $skillService = app(SkillService::class);
        $results = $skillService->processMobSkillTemplates($testMob, $testUser);

        if (!empty($results) && $results[0]['success']) {
            $this->info('✅ Скилл успешно применен!');

            // Проверяем созданный эффект
            $stunEffect = ActiveEffect::where('target_type', 'player')
                ->where('target_id', $testUser->id)
                ->where('effect_data->type', 'stun')
                ->where('ends_at', '>', now())
                ->first();

            if ($stunEffect) {
                $this->info('✅ Эффект стана создан:');
                $this->line("   ID: {$stunEffect->id}");
                $this->line("   Истекает: {$stunEffect->ends_at}");
                $this->line("   Длительность: " . now()->diffInSeconds($stunEffect->ends_at) . " секунд");

                $effectData = $stunEffect->effect_data;
                $this->line("   Данные эффекта:");
                $this->line("     - disable_skills: " . ($effectData['disable_skills'] ?? 'не задано'));
                $this->line("     - disable_movement: " . ($effectData['disable_movement'] ?? 'не задано'));
                $this->line("     - disable_actions: " . ($effectData['disable_actions'] ?? 'не задано'));
                $this->line("     - message: " . ($effectData['message'] ?? 'не задано'));
                $this->line("     - mob_skill_id: " . ($effectData['mob_skill_id'] ?? 'не задано'));

                // Проверяем длительность
                $expectedDuration = $mobSkill->getEffectiveDuration();
                $actualDuration = now()->diffInSeconds($stunEffect->ends_at);

                if (abs($actualDuration - $expectedDuration) <= 2) {
                    $this->info('✅ Длительность эффекта корректна!');
                } else {
                    $this->error("❌ Длительность эффекта некорректна! Ожидалось: {$expectedDuration}, получено: {$actualDuration}");
                }

                $this->newLine();
                $this->info('🌐 Проверьте интерфейс: http://127.0.0.1:8000/mine-locations/174');
                $this->info('🧹 Для очистки: php artisan test:heavy-strike --clear');

            } else {
                $this->error('❌ Эффект стана не найден!');
            }
        } else {
            $this->error('❌ Не удалось применить скилл!');
        }
    }
}
