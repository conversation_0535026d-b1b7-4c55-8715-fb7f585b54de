@props(['isStunned' => false, 'dungeon' => null])

{{--
Компонент для отображения блока навигации в подземельях
Принимает:
- isStunned: флаг оглушения игрока
- dungeon: модель подземелья
--}}

<div class="mt-3 mb-3 bg-[#2f2d2b] rounded-lg border-2 border-[#a6925e] shadow-lg overflow-hidden">
    {{-- Заголовок блока навигации --}}
    <div class="bg-gradient-to-b from-[#5a4d36] to-[#3a321c] border-b border-[#8c7a55] px-2 py-1.5">
        <h3 class="text-center text-[#e9d5a0] font-bold text-sm tracking-wide">Навигация по подземелью</h3>
    </div>

    {{-- Контент блока навигации --}}
    <div class="p-2">
        @php
            // Формируем массив доступных действий в подземелье
            $navigationOptions = [];

            // Основные действия подземелья
            $navigationOptions['dungeon_details'] = [
                'name' => 'Детали подземелья',
                'icon' => '🏰',
                'route' => route('dungeons.show', $dungeon),
                'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                'is_current' => false
            ];

            $navigationOptions['dungeon_lobby'] = [
                'name' => 'Лобби группы',
                'icon' => '👥',
                'route' => route('dungeons.lobby', $dungeon),
                'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                'is_current' => false
            ];

            // Убрано дублирование кнопки "Покинуть подземелье" - теперь она только под заголовком
            // $navigationOptions['leave_dungeon'] = [
            //     'name' => 'Покинуть подземелье',
            //     'icon' => '🚪',
            //     'form_route' => route('dungeons.leave', $dungeon),
            //     'disabled_message' => null, // Всегда доступно
            //     'is_current' => false,
            //     'is_form' => true,
            //     'is_danger' => true
            // ];

            // Ссылка на список подземелий
            $navigationOptions['dungeons_list'] = [
                'name' => 'Список подземелий',
                'icon' => '🗺️',
                'route' => route('dungeons.index'),
                'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                'is_current' => false
            ];
        @endphp

        {{-- Отображаем навигационные опции --}}
        <div class="grid grid-cols-1 gap-1.5">
            @foreach($navigationOptions as $key => $option)
                @if(isset($option['is_form']) && $option['is_form'])
                    {{-- Если это кнопка с формой (например, выход из подземелья) --}}
                    <form action="{{ $option['form_route'] }}" method="POST" class="m-0 p-0">
                        @csrf
                        <button type="submit"
                            class="w-full flex items-center px-3 py-2 rounded-md 
                                   {{ isset($option['is_danger']) && $option['is_danger'] 
                                      ? 'bg-gradient-to-b from-[#6c4539] to-[#4a2f26] border border-[#8c6b5e] text-[#fceac4] hover:from-[#7c5549] hover:to-[#5a3f36] hover:border-[#9c7b6e]' 
                                      : 'bg-[#2a2721] border border-[#514b3c] hover:bg-[#3a3631] hover:border-[#7a7666] text-[#e9d5a0]' }} 
                                   transition-all {{ $isStunned && $key !== 'leave_dungeon' ? 'opacity-50 cursor-not-allowed' : '' }}"
                            {{ $isStunned && $key !== 'leave_dungeon' ? 'disabled' : '' }}>
                            <span class="mr-2 text-lg">{{ $option['icon'] }}</span>
                            <div class="flex-1 text-left">
                                <div class="font-medium text-sm">{{ $option['name'] }}</div>
                                @if($option['disabled_message'])
                                    <div class="text-xs opacity-70">{{ $option['disabled_message'] }}</div>
                                @endif
                            </div>
                        </button>
                    </form>
                @else
                    {{-- Обычная ссылка --}}
                    <a href="{{ $isStunned && $key !== 'dungeons_list' && $key !== 'leave_dungeon' ? '#' : $option['route'] }}"
                        class="flex items-center px-3 py-2 rounded-md
                               {{ $option['is_current']
                                  ? 'bg-[#3a321c] border border-[#8c7a55] text-[#f0d89e]'
                                  : 'bg-[#2a2721] border border-[#514b3c] hover:bg-[#3a3631] hover:border-[#7a7666] text-[#e9d5a0]' }}
                               transition-all {{ $isStunned && $key !== 'dungeons_list' && $key !== 'leave_dungeon' ? 'opacity-40 grayscale cursor-not-allowed pointer-events-none bg-[#1a1814] border-[#3b3629]' : '' }}"
                        {{ $isStunned && $key !== 'dungeons_list' && $key !== 'leave_dungeon' ? 'onclick="event.preventDefault(); return false;"' : '' }}>
                        <span class="mr-2 text-lg">{{ $option['icon'] }}</span>
                        <div class="flex-1">
                            <div class="font-medium text-sm">{{ $option['name'] }}</div>
                            @if($option['disabled_message'])
                                <div class="text-xs opacity-70">{{ $option['disabled_message'] }}</div>
                            @endif
                        </div>
                    </a>
                @endif
            @endforeach
        </div>

        {{-- Дополнительная информация о подземелье --}}
        @if($dungeon)
            <div class="mt-3 pt-2 border-t border-[#514b3c]">
                <div class="text-center text-xs text-[#a6925e]">
                    <div class="flex justify-between items-center">
                        <span>Уровень: {{ $dungeon->min_level }}+</span>
                        <span>Игроков: {{ $dungeon->min_players }}-{{ $dungeon->max_players }}</span>
                    </div>
                    @if($dungeon->recommended_gs > 0)
                        <div class="mt-1 text-[#f28b38]">
                            Рекомендуемый GS: {{ $dungeon->recommended_gs }}
                        </div>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>
