@props(['isStunned' => false, 'routePrefix' => 'battle.outposts', 'currentLocation' => '', 'subLocations' => null])

{{--
Компонент для отображения блока навигации в локациях аванпоста
Принимает:
- isStunned: флаг оглушения игрока
- routePrefix: префикс маршрута для действий
- currentLocation: текущая локация (для подсветки активной)
- subLocations: подлокации аванпоста (опционально)
--}}

<div class="mt-3 mb-3 bg-[#2f2d2b] rounded-lg border-2 border-[#a6925e] shadow-lg overflow-hidden">
    {{-- Заголовок блока локаций --}}
    <div class="bg-gradient-to-b from-[#5a4d36] to-[#3a321c] border-b border-[#8c7a55] px-2 py-1.5">
        <h3 class="text-center text-[#e9d5a0] font-bold text-sm tracking-wide">Навигация по аванпосту</h3>
    </div>

    {{-- Контейнер для локаций --}}
    <div class="p-2 space-y-1.5">
        @php
            // Определяем, какие локации отображать
            $locations = [];

            // Используем динамические локации для пользовательских аванпостов
            if ($routePrefix === 'battle.outposts' && request()->route('id')) {
                // Добавляем текущую локацию
                // Получаем информацию о текущей локации из базы данных
                // Получаем ID локации из маршрута
                $currentId = request()->route('id');
                $currentOutpostLocation = \App\Models\OutpostLocation::where('id', $currentId)->first();

                // Логируем информацию о текущей локации для отладки
                \Illuminate\Support\Facades\Log::info('Информация о текущей локации аванпоста в навигации', [
                    'requested_id' => $currentId,
                    'found_location' => $currentOutpostLocation ? true : false,
                    'location_name' => $currentOutpostLocation ? $currentOutpostLocation->name : 'не найдена',
                    'current_location_param' => $currentLocation
                ]);

                $locationName = $currentOutpostLocation ? $currentOutpostLocation->name : $currentLocation;

                // Используем ID локации из найденной записи или из маршрута
                $currentId = $currentOutpostLocation ? $currentOutpostLocation->id : request()->route('id');

                // Проверяем, что ID не пустой и локация найдена
                if (empty($currentId) || !$currentOutpostLocation) {
                    // Если ID пустой или локация не найдена, логируем ошибку и используем запасной вариант
                    \Illuminate\Support\Facades\Log::error('Проблема с ID для текущей локации аванпоста', [
                        'location_name' => $locationName,
                        'current_location' => $currentLocation,
                        'requested_id' => request()->route('id'),
                        'found_location' => $currentOutpostLocation ? true : false
                    ]);

                    // Используем route('battle.outposts.index') как запасной вариант
                    $locations['current_location'] = [
                        'name' => $locationName,
                        'icon' => '🏘️',
                        'route' => route('battle.outposts.index'),
                        'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                        'is_current' => true
                    ];
                } else {
                    // Если ID не пустой и локация найдена, используем его для формирования маршрута
                    $locations['current_location'] = [
                        'name' => $locationName,
                        'icon' => '🏘️',
                        'route' => route('battle.outposts.show', ['id' => $currentId]),
                        'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                        'is_current' => true
                    ];

                    // Логируем успешное формирование маршрута
                    \Illuminate\Support\Facades\Log::info('Сформирован маршрут для текущей локации аванпоста', [
                        'location_name' => $locationName,
                        'outpost_id' => $currentId,
                        'route' => route('battle.outposts.show', ['id' => $currentId])
                    ]);
                }

                // Добавляем подлокации, если они есть
                if ($subLocations) {
                    foreach ($subLocations as $subLocation) {
                        // Проверяем, что подлокация существует и активна
                        $subLocationExists = \App\Models\OutpostLocation::where('id', $subLocation->id)
                            ->where('is_active', true)
                            ->exists();

                        if ($subLocationExists) {
                            // Используем ID подлокации для формирования ключа и маршрута
                            $locations['sublocation_' . $subLocation->id] = [
                                'name' => $subLocation->name,
                                'icon' => '🏠',
                                'route' => route('battle.outposts.show', ['id' => $subLocation->id]),
                                'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                                'is_current' => $subLocation->id == $currentId
                            ];

                            // Логируем информацию о подлокации
                            \Illuminate\Support\Facades\Log::info('Добавлена подлокация аванпоста в навигацию', [
                                'sublocation_name' => $subLocation->name,
                                'sublocation_id' => $subLocation->id,
                                'is_current' => $subLocation->id == $currentId
                            ]);
                        } else {
                            // Логируем ошибку, если подлокация не существует или не активна
                            \Illuminate\Support\Facades\Log::warning('Подлокация аванпоста не существует или не активна', [
                                'sublocation_id' => $subLocation->id,
                                'sublocation_name' => $subLocation->name
                            ]);
                        }
                    }
                }
            }

            // Добавляем ссылку на список аванпостов
            $locations['outposts_list'] = [
                'name' => 'Список аванпостов',
                'icon' => '🗺️',
                'route' => route('battle.outposts.index'),
                'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                'is_current' => false
            ];

            // Добавляем кнопку выхода из локации
            $locations['leave_location'] = [
                'name' => 'Покинуть локацию',
                'icon' => '🚪',
                'route' => '#',
                'form_route' => route($routePrefix . '.leave-location'),
                'is_form' => true,
                'disabled_message' => $isStunned ? '(недоступно: оглушение)' : null,
                'is_current' => false
            ];
        @endphp

        {{-- Отображаем локации --}}
        <div class="grid grid-cols-1 gap-1.5">
            @foreach($locations as $key => $location)
                @if(isset($location['is_form']) && $location['is_form'])
                    {{-- Если это кнопка с формой (например, выход из локации) --}}
                    <form action="{{ $location['form_route'] }}" method="POST" class="m-0 p-0">
                        @csrf
                        <button type="submit"
                            class="w-full flex items-center px-3 py-2 rounded-md {{ $location['is_current'] ? 'bg-[#3a321c] border border-[#8c7a55]' : 'bg-[#2a2721] border border-[#514b3c] hover:bg-[#3a3631] hover:border-[#7a7666]' }} transition-all {{ $isStunned && $key !== 'outposts_list' && $key !== 'leave_location' ? 'opacity-40 grayscale cursor-not-allowed bg-[#1a1814] border-[#3b3629]' : '' }}"
                            {{ $isStunned && $key !== 'outposts_list' && $key !== 'leave_location' ? 'disabled' : '' }}>
                            <span class="mr-2 text-lg">{{ $location['icon'] }}</span>
                            <span
                                class="text-sm {{ $location['is_current'] ? 'text-[#e9d5a0] font-semibold' : 'text-[#d3c6a6]' }}">
                                {{ $location['name'] }}
                            </span>
                            @if($location['disabled_message'])
                                <span class="ml-auto text-xs text-[#7a7666]">{{ $location['disabled_message'] }}</span>
                            @endif
                        </button>
                    </form>
                @else
                    {{-- Обычная ссылка --}}
                    <a href="{{ $isStunned && $key !== 'outposts_list' && $key !== 'leave_location' ? '#' : $location['route'] }}"
                        class="flex items-center px-3 py-2 rounded-md {{ $location['is_current'] ? 'bg-[#3a321c] border border-[#8c7a55]' : 'bg-[#2a2721] border border-[#514b3c] hover:bg-[#3a3631] hover:border-[#7a7666]' }} transition-all {{ $isStunned && $key !== 'outposts_list' && $key !== 'leave_location' ? 'opacity-40 grayscale cursor-not-allowed bg-[#1a1814] border-[#3b3629] pointer-events-none' : '' }}"
                        {{ $isStunned && $key !== 'outposts_list' && $key !== 'leave_location' ? 'onclick="event.preventDefault(); return false;"' : '' }}>
                        <span class="mr-2 text-lg">{{ $location['icon'] }}</span>
                        <span class="text-sm {{ $location['is_current'] ? 'text-[#e9d5a0] font-semibold' : 'text-[#d3c6a6]' }}">
                            {{ $location['name'] }}
                        </span>
                        @if($location['disabled_message'])
                            <span class="ml-auto text-xs text-[#7a7666]">{{ $location['disabled_message'] }}</span>
                        @endif
                    </a>
                @endif
            @endforeach
        </div>
    </div>
</div>