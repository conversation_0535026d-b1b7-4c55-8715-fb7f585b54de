<?php

use App\Http\Middleware\CheckPlayerStun;
use App\Http\Middleware\HandleActiveEffects;
use App\Http\Middleware\ResetUserTargetOnLocationChange;
use App\Http\Middleware\ResetUserTargets;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Http\Middleware\UserActivity;
use App\Http\Middleware\SessionSingleMiddleware;
use App\Http\Middleware\ResetTargetOnLocationChange;
use App\Http\Middleware\CheckUserHealth;
use App\Http\Middleware\CheckMinimumHP;
use App\Http\Middleware\CheckUserLevel;
use App\Http\Middleware\ThrottleBlacksmithRequests;
use App\Http\Middleware\JsonResponseMiddleware;
use App\Http\Middleware\CheckPlayerDefeat;
use App\Http\Middleware\ValidateOutpostLocation;
use App\Http\Middleware\CheckGameState;
use App\Http\Middleware\InventoryOptimization;
use App\Http\Middleware\CheckAccessKey;
use App\Http\Middleware\AdminAccess;
use App\Http\Middleware\PreventDirectAccess;
use App\Http\Middleware\SecurityAudit;
use App\Http\Middleware\CustomAuthenticate;
use App\Http\Middleware\BBCodePermissionMiddleware;
use App\Http\Middleware\LocationAccessMiddleware;
use App\Http\Middleware\MaintenanceModeMiddleware;
use App\Http\Middleware\CheckDungeonAccess;
use App\Http\Middleware\ResetPartyReadinessOnLocationChange;
use App\Http\Middleware\CheckForcedDungeonRedirect;
use App\Http\Middleware\CheckDungeonCompletionRedirect;
use App\Http\Middleware\IncreaseUploadLimits;
use App\Http\Middleware\StaticCacheMiddleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: [
            __DIR__ . '/../routes/web.php',
            __DIR__ . '/../routes/top-players.php',
            __DIR__ . '/../routes/party.php',
            __DIR__ . '/../routes/dungeons.php',
            __DIR__ . '/../routes/admin/locations.php',
            __DIR__ . '/../routes/admin/mines.php',
            __DIR__ . '/../routes/admin/outposts.php',
            __DIR__ . '/../routes/admin/dungeons.php',
        ],
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withCommands([
        // НОВАЯ СИСТЕМА БОТОВ АВАНПОСТОВ (БЕЗ LUA) - ОСНОВНЫЕ КОМАНДЫ
        \App\Console\Commands\Outpost\ProcessOutpostBots::class,
        \App\Console\Commands\Outpost\TestOutpostBotSystem::class,
        \App\Console\Commands\Outpost\DiagnoseOutpostBotSystem::class,
        \App\Console\Commands\TestCompleteOutpostSystem::class,
        \App\Console\Commands\TestNewSystemQuick::class,

        // ОПТИМИЗИРОВАННАЯ СИСТЕМА БОТОВ - РЕШЕНИЕ ПРОБЛЕМ ПРОИЗВОДИТЕЛЬНОСТИ
        \App\Console\Commands\Outpost\ProcessOutpostBotsOptimized::class,
        \App\Console\Commands\Outpost\MonitorBotPerformance::class,
        \App\Console\Commands\Outpost\DiagnoseBotPerformance::class,
        \App\Console\Commands\Outpost\TestBotPerformance::class,

        // Вспомогательные команды для аванпостов
        \App\Console\Commands\ListOutposts::class,
        \App\Console\Commands\DiagnoseBotAttackIssue::class,
        \App\Console\Commands\TestAllBotsAttack::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ УМНОГО РАСПРЕДЕЛЕНИЯ ЦЕЛЕЙ
        \App\Console\Commands\TestBotDistribution::class,
        \App\Console\Commands\MonitorBotDistributionPerformance::class,

        // НОВАЯ СИСТЕМА УМНОГО РАСПРЕДЕЛЕНИЯ ЦЕЛЕЙ - КОМАНДЫ ТЕСТИРОВАНИЯ
        \App\Console\Commands\TestBotDistribution::class,

        // НОВАЯ СИСТЕМА БОТОВ РУДНИКОВ (БЕЗ LUA) - ОСНОВНЫЕ КОМАНДЫ
        \App\Console\Commands\Mine\ProcessMineBots::class,
        \App\Console\Commands\Mine\TestMineBotSystem::class,
        \App\Console\Commands\Mine\DiagnoseMineBotsAttacks::class,
        \App\Console\Commands\Mine\DiagnoseBattleLogs::class,

        // ТЕСТИРОВАНИЕ И ДИАГНОСТИКА СИСТЕМЫ
        \App\Console\Commands\TestSchedulerFix::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ СИСТЕМЫ РАСПЛАВКИ ПРЕДМЕТОВ
        \App\Console\Commands\TestResourceStacking::class,
        \App\Console\Commands\TestItemMelting::class,
        \App\Console\Commands\CleanupMeltingTests::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ СИСТЕМЫ ЭКИПИРОВКИ
        \App\Console\Commands\TestSecondaryWeaponDisplay::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ ИСПРАВЛЕНИЙ ПРОФИЛЯ
        \App\Console\Commands\TestProfileFixes::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ СИСТЕМЫ ТЕХНИЧЕСКИХ РАБОТ
        \App\Console\Commands\TestMaintenanceWithUser::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ СИСТЕМЫ ОПЫТА И УРОВНЕЙ
        \App\Console\Commands\TestExperienceSystem::class,
        \App\Console\Commands\FixExperienceLevels::class,
        \App\Console\Commands\TestExperienceAwarding::class,
        \App\Console\Commands\DiagnoseProductionExperience::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ УНИФИЦИРОВАННОЙ СИСТЕМЫ ОНЛАЙН СТАТУСА
        \App\Console\Commands\OptimizedOnlineCleanup::class,
        \App\Console\Commands\TestUnifiedOnlineSystem::class,

        // НОВЫЕ КОМАНДЫ МОНИТОРИНГА И ТЕСТИРОВАНИЯ ОНЛАЙН СТАТУСА
        \App\Console\Commands\OnlineStatus\MonitorPerformanceCommand::class,
        \App\Console\Commands\OnlineStatus\LoadTestCommand::class,
        \App\Console\Commands\OnlineStatus\MigrationPlanCommand::class,

        // КОМАНДЫ ДИАГНОСТИКИ ПРОДАКШЕНА
        \App\Console\Commands\DiagnoseProductionTables::class,
        \App\Console\Commands\AnalyzeTableStructureDifferences::class,
        \App\Console\Commands\SafeProductionMigration::class,
        \App\Console\Commands\ValidateProductionMigration::class,
        \App\Console\Commands\TestProductionFix::class,
        \App\Console\Commands\FixProductionStructure::class,
        \App\Console\Commands\RunProductionMigrations::class,

        // КОМАНДЫ ТЕСТИРОВАНИЯ ИСПРАВЛЕНИЙ ОНЛАЙН СТАТУСА
        \App\Console\Commands\TestOnlineStatusFixes::class,
    ])
    ->withMiddleware(function (Middleware $middleware) {
        // Добавляем middleware в группу web
        // КРИТИЧЕСКИ ВАЖНО: MaintenanceModeMiddleware должен быть ПОСЛЕ сессионных middleware,
        // но ДО остальных middleware для корректной работы авторизации
        $middleware->web(append: [
            StaticCacheMiddleware::class, // ОПТИМИЗАЦИЯ: Агрессивное кэширование статических ресурсов (ПЕРВЫМ!)
            MaintenanceModeMiddleware::class, // Проверка режима технических работ с доступом разработчиков (ПОСЛЕ сессий!)
            SessionSingleMiddleware::class, // Расскомментировано и добавлено в группу web
            CheckPlayerDefeat::class, // Добавляем middleware для проверки статуса поражения
            CheckGameState::class, // Добавляем middleware для проверки состояния игры и обелисков
            \App\Http\Middleware\AdminDebugMode::class, // Добавляем middleware для отладочного режима администраторов
                // CheckAccessKey::class, // УБРАНО: Заменено на MaintenanceModeMiddleware с доступом разработчиков
                // SecurityAudit::class, // УБРАНО: Больше не нужен аудит системы ключей
            ResetPartyReadinessOnLocationChange::class, // Сброс готовности группы при смене локации
        ]);

        // Также можно добавить глобально
        $middleware->append([
            // SessionSingleMiddleware::class,
        ]);

        // Регистрируем ваше middleware
        $middleware->alias([
            'activity' => UserActivity::class,
            'user.activity' => UserActivity::class,
            'reset.targets' => ResetUserTargets::class,
            'check.user.minimumHP' => CheckMinimumHP::class,
            'check.user.health' => CheckUserHealth::class,
            'check.user.level' => CheckUserLevel::class,
            'resetTarget.OnLocationChange' => ResetUserTargetOnLocationChange::class,
            'handle.ActiveEffects' => HandleActiveEffects::class,
            'сheck.user.stun' => CheckPlayerStun::class,
            'check.stun.actions' => \App\Http\Middleware\CheckPlayerStunActions::class, // Блокировка всех действий при стане
            'auth.single_session' => SessionSingleMiddleware::class, // Добавляем алиас
            'throttle.blacksmith' => ThrottleBlacksmithRequests::class, // Добавляем middleware для дросселирования запросов к кузнецу
            'json.response' => JsonResponseMiddleware::class, // Добавляем middleware для обеспечения JSON-ответов
            'check.player.defeat' => CheckPlayerDefeat::class, // Добавляем middleware для проверки статуса поражения
            'admin' => \App\Http\Middleware\AdminMiddleware::class, // Добавляем middleware для доступа к админке
            'check.prologue' => \App\Http\Middleware\CheckPrologueUser::class, // Добавляем middleware для проверки пользователей пролога
            'admin.debug' => \App\Http\Middleware\AdminDebugMode::class, // Добавляем middleware для отладочного режима администраторов
            'validate.outpost' => ValidateOutpostLocation::class, // Добавляем middleware для валидации локаций аванпостов
            'check.game.state' => CheckGameState::class, // Добавляем middleware для проверки состояния игры
            'inventory.optimization' => InventoryOptimization::class, // Добавляем middleware для оптимизации инвентаря
            'access.key' => CheckAccessKey::class, // Добавляем middleware для проверки ключей доступа
            'admin.access' => AdminAccess::class, // Добавляем middleware для административного доступа
            'prevent.direct' => PreventDirectAccess::class, // Добавляем middleware для предотвращения прямого доступа
            'security.audit' => SecurityAudit::class, // Добавляем middleware для аудита безопасности
            'check.forced.redirect' => CheckForcedDungeonRedirect::class, // Добавляем middleware для принудительного редиректа в подземельях
            'check.dungeon.completion' => CheckDungeonCompletionRedirect::class, // Добавляем middleware для перенаправления на завершение подземелья
            'auth' => CustomAuthenticate::class, // Переопределяем стандартный middleware аутентификации
            'check.dungeon.access' => CheckDungeonAccess::class, // Добавляем middleware для проверки доступа в подземелье
            'bbcode.permission' => BBCodePermissionMiddleware::class, // Добавляем middleware для проверки прав на BB-коды
            'location.access' => LocationAccessMiddleware::class, // Добавляем middleware для проверки доступа к локациям
            'maintenance' => MaintenanceModeMiddleware::class, // Добавляем middleware для режима технических работ
            'party.leader' => \App\Http\Middleware\PartyLeaderMiddleware::class, // Добавляем middleware для проверки лидерства в группе
            'reset.party.readiness' => ResetPartyReadinessOnLocationChange::class, // Добавляем middleware для сброса готовности при смене локации
            'upload.limits' => IncreaseUploadLimits::class, // Добавляем middleware для увеличения лимитов загрузки
            'static.cache' => StaticCacheMiddleware::class, // Добавляем middleware для кэширования статических ресурсов
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->create();

// ЗАЩИТА: Запрещаем опасные команды в продакшене
if (app()->environment('production')) {
    \Illuminate\Support\Facades\DB::prohibitDestructiveCommands();
}
