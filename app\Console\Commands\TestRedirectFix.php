<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TestRedirectFix extends Command
{
    protected $signature = 'test:redirect-fix';
    protected $description = 'Тестирование исправлений циклических переадресаций';

    public function handle()
    {
        $this->info('🧪 ТЕСТИРОВАНИЕ ИСПРАВЛЕНИЙ ПЕРЕАДРЕСАЦИЙ');
        $this->newLine();

        $baseUrl = 'http://127.0.0.1:8000';
        
        // Тестируемые страницы
        $testPages = [
            '/' => 'Главная страница',
            '/home' => 'Домашняя страница',
            '/dashboard' => 'Панель управления',
            '/battle' => 'Битва',
            '/inventory' => 'Инвентарь',
            '/shop' => 'Магазин',
        ];

        $this->info('1. 🌐 Тестирование доступности страниц:');
        
        foreach ($testPages as $path => $description) {
            $this->testPage($baseUrl . $path, $description);
        }

        $this->newLine();
        $this->info('2. 🔄 Тестирование защиты от циклических переадресаций:');
        
        // Симулируем множественные запросы к одной странице
        $this->testMultipleRequests($baseUrl . '/home', 'Домашняя страница');

        $this->newLine();
        $this->info('✅ Тестирование завершено');
    }

    private function testPage(string $url, string $description): void
    {
        try {
            $response = Http::timeout(10)
                ->withOptions(['allow_redirects' => ['max' => 5]]) // Ограничиваем количество переадресаций
                ->get($url);

            $statusCode = $response->status();
            $redirectCount = $this->getRedirectCount($response);

            if ($statusCode === 200) {
                $status = '✅ OK';
            } elseif ($statusCode >= 300 && $statusCode < 400) {
                $status = '🔄 REDIRECT';
            } else {
                $status = '❌ ERROR';
            }

            $this->line("   {$status} {$description} ({$statusCode}) - Переадресаций: {$redirectCount}");

            // Проверяем на подозрительное количество переадресаций
            if ($redirectCount > 3) {
                $this->warn("     ⚠️ Подозрительно много переадресаций!");
            }

        } catch (\Exception $e) {
            $this->error("   ❌ {$description} - Ошибка: " . $e->getMessage());
        }
    }

    private function testMultipleRequests(string $url, string $description): void
    {
        $this->line("   Тестирование множественных запросов к: {$description}");
        
        $redirectCounts = [];
        
        for ($i = 1; $i <= 5; $i++) {
            try {
                $response = Http::timeout(5)
                    ->withOptions(['allow_redirects' => ['max' => 10]])
                    ->get($url);
                
                $redirectCount = $this->getRedirectCount($response);
                $redirectCounts[] = $redirectCount;
                
                $this->line("     Запрос {$i}: {$response->status()} - Переадресаций: {$redirectCount}");
                
                // Небольшая пауза между запросами
                usleep(500000); // 0.5 секунды
                
            } catch (\Exception $e) {
                $this->error("     Запрос {$i}: Ошибка - " . $e->getMessage());
            }
        }
        
        $maxRedirects = max($redirectCounts);
        $avgRedirects = round(array_sum($redirectCounts) / count($redirectCounts), 2);
        
        $this->line("   Статистика: Макс. переадресаций: {$maxRedirects}, Среднее: {$avgRedirects}");
        
        if ($maxRedirects > 5) {
            $this->warn("   ⚠️ Обнаружены потенциальные циклы переадресаций!");
        } else {
            $this->info("   ✅ Защита от циклов работает корректно");
        }
    }

    private function getRedirectCount($response): int
    {
        // Попытка получить количество переадресаций из заголовков
        // Это приблизительная оценка, так как Guzzle не предоставляет точную информацию
        $history = $response->transferStats?->getHandlerStats()['redirect_count'] ?? 0;
        return $history;
    }
}
