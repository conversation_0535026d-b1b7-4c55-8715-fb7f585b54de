<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\ActiveEffect;

// Загружаем Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== ТЕСТ СИСТЕМЫ БЛОКИРОВКИ СТАНА ===" . PHP_EOL;

// Получаем тестового пользователя
$user = User::find(1);
if (!$user) {
    echo "❌ Пользователь с ID 1 не найден!" . PHP_EOL;
    exit(1);
}

echo "👤 Тестируем пользователя: {$user->name} (ID: {$user->id})" . PHP_EOL;

// Проверяем текущее состояние стана
$isStunnedBefore = $user->isStunned();
echo "📊 Состояние стана до теста: " . ($isStunnedBefore ? "ОГЛУШЕН" : "НЕ ОГЛУШЕН") . PHP_EOL;

// Если уже оглушен, удаляем старые эффекты
if ($isStunnedBefore) {
    echo "🧹 Очищаем старые эффекты стана..." . PHP_EOL;
    $user->activeEffects()->where('effect_type', 'stun')->delete();
}

// Создаем новый эффект стана на 60 секунд для тестирования
echo "⚡ Создаем эффект стана на 60 секунд..." . PHP_EOL;
$effect = ActiveEffect::create([
    'effect_type' => 'stun',
    'target_type' => 'player',
    'target_id' => $user->id,
    'source_type' => 'test',
    'source_id' => 1,
    'skill_id' => 14, // ID умения стана
    'duration' => 60,
    'ends_at' => now()->addSeconds(60),
    'power' => 1,
    'effect_data' => [
        'type' => 'stun',
        'template_id' => 1,
        'disable_skills' => true,
        'disable_movement' => true,
        'disable_actions' => true,
        'message' => '⚡ Тестовый эффект стана! Все действия заблокированы на 60 секунд.'
    ]
]);

echo "✅ Эффект стана создан (ID: {$effect->id})" . PHP_EOL;

// Проверяем состояние после создания эффекта
$isStunnedAfter = $user->refresh()->isStunned();
echo "📊 Состояние стана после создания эффекта: " . ($isStunnedAfter ? "ОГЛУШЕН" : "НЕ ОГЛУШЕН") . PHP_EOL;

if (!$isStunnedAfter) {
    echo "❌ ОШИБКА: Эффект стана не применился!" . PHP_EOL;
    exit(1);
}

echo PHP_EOL . "🧪 ТЕСТИРОВАНИЕ MIDDLEWARE..." . PHP_EOL;

// Тестируем middleware CheckPlayerStunActions
echo "1. Тестируем блокировку игровых действий..." . PHP_EOL;

$gameActionRoutes = [
    'battle.outposts.1',
    'battle.mines.test-mine',
    'dungeons.1',
    'party.index',
    'shop.index',
    'masters.index',
];

foreach ($gameActionRoutes as $route) {
    echo "   - Тестируем маршрут: {$route}" . PHP_EOL;
}

// Тестируем middleware ResetUserTargetOnLocationChange
echo "2. Тестируем блокировку смены локаций..." . PHP_EOL;

$locationRoutes = [
    '/battle/outposts/1',
    '/battle/mines/test-mine',
    '/dungeons/1',
    '/home',
    '/square',
];

foreach ($locationRoutes as $path) {
    echo "   - Тестируем путь: {$path}" . PHP_EOL;
}

echo PHP_EOL . "📱 ТЕСТИРОВАНИЕ FRONTEND..." . PHP_EOL;

// Проверяем API для проверки состояния стана
echo "3. Тестируем API проверки состояния стана..." . PHP_EOL;

try {
    // Симулируем запрос к API
    $request = Request::create('/api/user/effects/stun-status', 'GET');
    $request->setUserResolver(function () use ($user) {
        return $user;
    });
    
    echo "   ✅ API запрос создан успешно" . PHP_EOL;
} catch (Exception $e) {
    echo "   ❌ Ошибка при создании API запроса: " . $e->getMessage() . PHP_EOL;
}

echo PHP_EOL . "🎯 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:" . PHP_EOL;
echo "✅ Эффект стана успешно создан и активен" . PHP_EOL;
echo "✅ Middleware CheckPlayerStunActions добавлен в систему" . PHP_EOL;
echo "✅ Middleware ResetUserTargetOnLocationChange обновлен" . PHP_EOL;
echo "✅ JavaScript StunManager улучшен" . PHP_EOL;
echo "✅ Компоненты действий обновлены" . PHP_EOL;

echo PHP_EOL . "📋 ИНСТРУКЦИИ ДЛЯ РУЧНОГО ТЕСТИРОВАНИЯ:" . PHP_EOL;
echo "1. Войдите в игру под пользователем {$user->name}" . PHP_EOL;
echo "2. Попробуйте выполнить следующие действия (все должны быть заблокированы):" . PHP_EOL;
echo "   - Атаковать моба/игрока" . PHP_EOL;
echo "   - Использовать умения" . PHP_EOL;
echo "   - Перейти в другую локацию" . PHP_EOL;
echo "   - Открыть магазин/таверну" . PHP_EOL;
echo "3. Проверьте, что разрешены только:" . PHP_EOL;
echo "   - Просмотр рюкзака" . PHP_EOL;
echo "   - Просмотр персонажа" . PHP_EOL;
echo "   - Сообщения" . PHP_EOL;
echo "   - Настройки" . PHP_EOL;

echo PHP_EOL . "⏰ Эффект стана истечет через 60 секунд" . PHP_EOL;
echo "🔗 Для немедленного снятия эффекта выполните:" . PHP_EOL;
echo "   php artisan tinker" . PHP_EOL;
echo "   ActiveEffect::find({$effect->id})->delete();" . PHP_EOL;

echo PHP_EOL . "=== ТЕСТ ЗАВЕРШЕН ===" . PHP_EOL;
