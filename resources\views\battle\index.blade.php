<!DOCTYPE html>
<html lang="en">
<?php
use Illuminate\Support\Facades\Auth;
use App\Models\GuildInvitation;
?>

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Битва - Echoes of Eternity</title>

    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/battle/tooltips.js', 'resources/css/battle/tooltips.css', 'resources/css/components/guild-invitation.css', 'resources/css/components/donation-button.css'])
</head>

<body class="bg-[#2f2d2b] text-[#f5f5f5] font-serif flex flex-col min-h-screen">
    {{-- Основной контейнер --}}
    <div class="container max-w-md mx-auto px-1 py-0 border-2 border-[#a6925e] rounded-lg flex-grow">

        {{-- HP/MP блок с уведомлениями --}}
        <x-layout.hp-mp-bar :actualResources="$actualResources" :userProfile="$userProfile">
            {{-- Слот для уведомлений между HP и MP --}}
            <x-layout.notifications-bar :hasUnreadMessages="$hasUnreadMessages ?? false"
                :unreadMessagesCount="$unreadMessagesCount ?? 0" :hasBrokenItems="$hasBrokenItems ?? false"
                :brokenItemsCount="$brokenItemsCount ?? 0" />
        </x-layout.hp-mp-bar>


        {{-- Отображение валюты с прогрессом опыта --}}
        <x-layout.currency-display :userProfile="$userProfile" :experienceProgress="$experienceProgress ?? null" />

        {{-- Приветственное сообщение --}}
        <div class="text-center flex justify-center space-x-1 max-w-[300px] mx-auto">
            <x-battle.welcome-message />
        </div>

        {{-- Уведомление о приглашении в гильдию --}}
        @if(Auth::check())
            @php
                $guildInvitation = Auth::user()->getLatestGuildInvitation();
            @endphp
            <x-layout.guild-invitation :guildInvitation="$guildInvitation" />
        @endif

        {{-- Хлебные крошки (без блока изображения) --}}
        <div class="w-full mx-auto">
            <x-breadcrumbs :breadcrumbs="$breadcrumbs" />
            {{-- Декоративная HR-линия --}}
            <div class="px-4 py-0">
                <hr class="border-0 h-px bg-gradient-to-r from-transparent via-[#e5b769] to-transparent opacity-70">
            </div>
            {{-- Название локации --}}
            <x-layout.location-name title="Битва" />
        </div>
        {{-- Flash-сообщения --}}
        <x-game-flash-messages />

        {{-- Основной контент - Меню боевых локаций --}}
        <x-battle.location-menu />
    </div>

    {{-- Нижние кнопки навигации --}}
    @php
        $isStunned = Auth::user()->isStunned();
    @endphp
    <x-layout.navigation-buttons :isDisabled="$isStunned" />

    {{-- Футер --}}
    <x-layout.footer :onlineCount="$onlineCount" />

    {{-- Мобильные всплывающие подсказки --}}
    <x-battle.tooltip-modal id="dungeon-tooltip" title="Подземелье" icon="assets/iconDungeon.png"
        description="Исследуйте древние подземелья, сражайтесь с боссами и находите легендарные сокровища."
        releaseDate="Ожидаемая дата выхода: скоро" />

    <x-battle.tooltip-modal id="trial-tooltip" title="Испытание" icon="assets/iconTrial.png"
        description="Пройдите серию испытаний, проверьте свои навыки и получите уникальные награды за достижения."
        releaseDate="Ожидаемая дата выхода: следующее обновление" />

    <x-battle.tooltip-modal id="dominions-tooltip" title="Доминионы" icon="assets/iconDominions.png"
        description="Масштабные сражения за территории между гильдиями. Захватывайте замки, собирайте дань и расширяйте свое влияние."
        releaseDate="Ожидаемая дата выхода: крупное обновление (Q3 2025)" :requirements="[
        ['icon' => '👤', 'text' => 'Уровень персонажа 30+'],
        ['icon' => '🏰', 'text' => 'Членство в гильдии']
    ]" />

    <x-battle.tooltip-modal id="events-tooltip" title="Временные события" icon="assets/iconTemporary.png"
        description="Особые ограниченные по времени события с уникальными противниками, заданиями и наградами. Не пропустите!"
        releaseDate="Первое событие: Праздник Зимнего Солнцестояния (декабрь 2025)" plannedEvents="8" />

</body>

</html>