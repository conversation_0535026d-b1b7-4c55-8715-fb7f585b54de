<?php

namespace App\Console\Commands;

use App\Models\SpawnedResource;
use App\Services\LocationResourceService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DiagnoseResourceRespawn extends Command
{
    protected $signature = 'diagnose:resource-respawn {--fix : Исправить найденные проблемы}';
    protected $description = 'Диагностика проблем с респавном ресурсов';

    public function handle()
    {
        $this->info('🔍 Диагностика системы респавна ресурсов...');
        $this->newLine();

        // Шаг 1: Проверяем ресурсы с 0 прочностью
        $this->info('1️⃣ Проверка ресурсов с нулевой прочностью...');
        $zeroHealthResources = SpawnedResource::where('durability', '<=', 0)->get();
        
        $this->line("   📊 Всего ресурсов с 0 прочностью: {$zeroHealthResources->count()}");
        
        $activeZeroHealth = $zeroHealthResources->where('is_active', true);
        $inactiveZeroHealth = $zeroHealthResources->where('is_active', false);
        
        $this->line("   ✅ Активных с 0 прочностью: {$activeZeroHealth->count()}");
        $this->line("   ❌ Неактивных с 0 прочностью: {$inactiveZeroHealth->count()}");
        
        if ($activeZeroHealth->count() > 0) {
            $this->warn("   ⚠️ ПРОБЛЕМА: Найдены активные ресурсы с 0 прочностью!");
            $this->line("   🔧 Эти ресурсы должны быть неактивными и иметь время респавна");
            
            foreach ($activeZeroHealth->take(5) as $resource) {
                $this->line("      - ID: {$resource->id}, Локация: {$resource->location_id}, Прочность: {$resource->durability}");
            }
        }
        $this->newLine();

        // Шаг 2: Проверяем неактивные ресурсы без времени респавна
        $this->info('2️⃣ Проверка неактивных ресурсов без времени респавна...');
        $inactiveNoRespawn = SpawnedResource::where('is_active', false)
            ->whereNull('respawn_at')
            ->get();
        
        $this->line("   📊 Неактивных ресурсов без времени респавна: {$inactiveNoRespawn->count()}");
        
        if ($inactiveNoRespawn->count() > 0) {
            $this->warn("   ⚠️ ПРОБЛЕМА: Найдены неактивные ресурсы без времени респавна!");
            
            foreach ($inactiveNoRespawn->take(5) as $resource) {
                $this->line("      - ID: {$resource->id}, Локация: {$resource->location_id}, Время респавна: {$resource->respawn_time}мин");
            }
        }
        $this->newLine();

        // Шаг 3: Проверяем ресурсы готовые к респавну
        $this->info('3️⃣ Проверка ресурсов готовых к респавну...');
        $readyToRespawn = SpawnedResource::where('is_active', false)
            ->where('respawn_at', '<=', Carbon::now())
            ->get();
        
        $this->line("   📊 Ресурсов готовых к респавну: {$readyToRespawn->count()}");
        
        if ($readyToRespawn->count() > 0) {
            $this->line("   ✅ Найдены ресурсы готовые к респавну:");
            
            foreach ($readyToRespawn->take(5) as $resource) {
                $timeDiff = now()->diffInMinutes($resource->respawn_at);
                $this->line("      - ID: {$resource->id}, Время респавна: {$resource->respawn_at->format('H:i:s')}, Просрочен на: {$timeDiff}мин");
            }
        }
        $this->newLine();

        // Шаг 4: Проверяем ресурсы с некорректным временем респавна
        $this->info('4️⃣ Проверка ресурсов с некорректным временем респавна...');
        $incorrectRespawn = SpawnedResource::where('is_active', false)
            ->whereNotNull('respawn_at')
            ->where('respawn_time', '<=', 0)
            ->get();
        
        $this->line("   📊 Ресурсов с некорректным временем респавна: {$incorrectRespawn->count()}");
        
        if ($incorrectRespawn->count() > 0) {
            $this->warn("   ⚠️ ПРОБЛЕМА: Найдены ресурсы с некорректным временем респавна!");
            
            foreach ($incorrectRespawn->take(5) as $resource) {
                $this->line("      - ID: {$resource->id}, respawn_time: {$resource->respawn_time}");
            }
        }
        $this->newLine();

        // Шаг 5: Тестируем команду респавна
        $this->info('5️⃣ Тестирование команды респавна...');
        $beforeCount = SpawnedResource::where('is_active', false)->count();
        
        $service = app(LocationResourceService::class);
        $service->respawnResources();
        
        $afterCount = SpawnedResource::where('is_active', false)->count();
        $respawned = $beforeCount - $afterCount;
        
        $this->line("   📊 Неактивных ресурсов до респавна: {$beforeCount}");
        $this->line("   📊 Неактивных ресурсов после респавна: {$afterCount}");
        $this->line("   ✨ Респавнилось ресурсов: {$respawned}");
        $this->newLine();

        // Шаг 6: Исправления (если указан флаг --fix)
        if ($this->option('fix')) {
            $this->info('6️⃣ Исправление найденных проблем...');
            $fixed = 0;

            // Исправляем активные ресурсы с 0 прочностью
            if ($activeZeroHealth->count() > 0) {
                $this->line("   🔧 Исправляем активные ресурсы с 0 прочностью...");
                
                foreach ($activeZeroHealth as $resource) {
                    $resource->is_active = false;
                    $respawnTime = $resource->respawn_time ?? 60;
                    $resource->respawn_at = Carbon::now()->addMinutes($respawnTime);
                    $resource->save();
                    $fixed++;
                }
                
                $this->line("      ✅ Исправлено: {$activeZeroHealth->count()} ресурсов");
            }

            // Исправляем неактивные ресурсы без времени респавна
            if ($inactiveNoRespawn->count() > 0) {
                $this->line("   🔧 Исправляем неактивные ресурсы без времени респавна...");
                
                foreach ($inactiveNoRespawn as $resource) {
                    $respawnTime = $resource->respawn_time ?? 60;
                    $resource->respawn_at = Carbon::now()->addMinutes($respawnTime);
                    $resource->save();
                    $fixed++;
                }
                
                $this->line("      ✅ Исправлено: {$inactiveNoRespawn->count()} ресурсов");
            }

            // Исправляем ресурсы с некорректным временем респавна
            if ($incorrectRespawn->count() > 0) {
                $this->line("   🔧 Исправляем ресурсы с некорректным временем респавна...");
                
                foreach ($incorrectRespawn as $resource) {
                    $resource->respawn_time = 60; // Устанавливаем по умолчанию 60 минут
                    $resource->respawn_at = Carbon::now()->addMinutes(60);
                    $resource->save();
                    $fixed++;
                }
                
                $this->line("      ✅ Исправлено: {$incorrectRespawn->count()} ресурсов");
            }

            $this->line("   🎉 Всего исправлено проблем: {$fixed}");
            $this->newLine();
        }

        // Шаг 7: Рекомендации
        $this->info('7️⃣ Рекомендации:');
        
        $totalProblems = $activeZeroHealth->count() + $inactiveNoRespawn->count() + $incorrectRespawn->count();
        
        if ($totalProblems > 0) {
            $this->warn("   ⚠️ Найдено проблем: {$totalProblems}");
            $this->line("   💡 Запустите команду с флагом --fix для исправления:");
            $this->line("      php artisan diagnose:resource-respawn --fix");
        } else {
            $this->line("   ✅ Проблем не найдено! Система респавна работает корректно.");
        }
        
        if ($readyToRespawn->count() > 0) {
            $this->line("   💡 Есть ресурсы готовые к респавну. Запустите:");
            $this->line("      php artisan resources:respawn");
        }

        return Command::SUCCESS;
    }
}
